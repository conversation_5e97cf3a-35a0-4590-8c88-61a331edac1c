function engine = RunLAATSimRealTimeEngine(InflowRate, SceStr, asStr, varargin)
% RunLAATSimRealTimeEngine - 启动真正的实时LAAT仿真引擎
%
% 语法:
%   engine = RunLAATSimRealTimeEngine(InflowRate, SceStr, asStr, varargin)
%
% 描述:
%   这是一个完全重新设计的实时仿真系统，使用事件驱动架构和异步处理，
%   提供真正的实时仿真体验，而不是简单的批处理循环加可视化。
%
% 核心特性:
%   - 基于timer的真实时间驱动仿真
%   - 状态机管理仿真状态（运行/暂停/停止）
%   - 异步计算和渲染分离
%   - 交互式控制界面（播放/暂停/重置/速度控制）
%   - 实时数据流和可视化更新
%   - 键盘快捷键支持
%
% 输入参数:
%   InflowRate - 流入率（架/秒）
%   SceStr     - 场景名称字符串
%   asStr      - 空域类型字符串 ('NYC', 'SF', 'PAR' 等)
%   varargin   - 可选参数:
%                'TimeScale', scale - 时间缩放因子（默认1.0，即实时）
%                'FrameRate', fps - 渲染帧率（默认30fps）
%                'SimStepRate', hz - 仿真步进频率（默认20Hz）
%                'AutoStart', true/false - 是否自动开始仿真（默认false）
%
% 输出:
%   engine - RealTimeSimulationEngine对象，可用于控制仿真
%
% 使用示例:
%   % 基本实时仿真（手动控制）
%   engine = RunLAATSimRealTimeEngine(10/60, 'RealTime1', 'NYC');
%   
%   % 2倍速自动开始仿真
%   engine = RunLAATSimRealTimeEngine(15/60, 'FastSim', 'SF', ...
%                                    'TimeScale', 2.0, 'AutoStart', true);
%   
%   % 高帧率仿真（适合录制视频）
%   engine = RunLAATSimRealTimeEngine(8/60, 'HighFPS', 'PAR', ...
%                                    'FrameRate', 60, 'SimStepRate', 50);
%   
%   % 慢速仿真（适合详细观察）
%   engine = RunLAATSimRealTimeEngine(12/60, 'SlowMotion', 'NYC', ...
%                                    'TimeScale', 0.5);
%
% 交互控制:
%   界面控制:
%     - 播放/暂停按钮：控制仿真运行状态
%     - 重置按钮：重置仿真到初始状态
%     - 速度滑块：实时调整仿真速度（0.1x - 5.0x）
%     - 时间滑块：跳转到指定时间点
%   
%   键盘快捷键:
%     - 空格键：播放/暂停切换
%     - R键：重置仿真
%     - ESC键：停止仿真
%     - +/-键：增加/减少仿真速度
%
% 程序化控制:
%   engine.start()     - 开始仿真
%   engine.pause()     - 暂停仿真
%   engine.resume()    - 继续仿真
%   engine.stop()      - 停止仿真
%   engine.reset()     - 重置仿真
%
% 注意事项:
%   - 这是真正的实时仿真系统，与传统批处理模式完全不同
%   - 仿真时间与实际时间按TimeScale比例对应
%   - 可视化更新独立于仿真计算，保证界面响应性
%   - 支持仿真过程中的实时交互和参数调整
%   - 关闭窗口会自动清理所有资源
%
% 性能建议:
%   - 小规模仿真（<50架）：推荐30fps渲染，20Hz仿真
%   - 中等规模仿真（50-100架）：推荐20fps渲染，15Hz仿真
%   - 大规模仿真（>100架）：推荐15fps渲染，10Hz仿真
%
% 作者: AI Assistant
% 创建日期: 2024-09-26

% 解析输入参数
p = inputParser;
addRequired(p, 'InflowRate', @isnumeric);
addRequired(p, 'SceStr', @ischar);
addRequired(p, 'asStr', @ischar);
addParameter(p, 'TimeScale', 1.0, @(x) isnumeric(x) && x > 0);
addParameter(p, 'FrameRate', 30, @(x) isnumeric(x) && x > 0);
addParameter(p, 'SimStepRate', 20, @(x) isnumeric(x) && x > 0);
addParameter(p, 'AutoStart', false, @islogical);

parse(p, InflowRate, SceStr, asStr, varargin{:});

% 获取参数
timeScale = p.Results.TimeScale;
frameRate = p.Results.FrameRate;
simStepRate = p.Results.SimStepRate;
autoStart = p.Results.AutoStart;

% 显示启动信息
fprintf('\n=== 实时LAAT仿真引擎 ===\n');
fprintf('这是一个真正的实时仿真系统，基于事件驱动架构\n');
fprintf('=====================================\n');
fprintf('场景: %s\n', SceStr);
fprintf('空域: %s\n', asStr);
fprintf('流入率: %.3f 架/秒 (%.1f 架/分钟)\n', InflowRate, InflowRate*60);
fprintf('时间缩放: %.1fx %s\n', timeScale, ...
        timeScale == 1.0 ?'(实时)' : timeScale > 1.0 ? '(加速)' : '(慢速)');
fprintf('渲染帧率: %d fps\n', frameRate);
fprintf('仿真频率: %d Hz\n', simStepRate);
fprintf('自动开始: %s\n', autoStart ? '是' : '否');
fprintf('=====================================\n\n');

fprintf('核心特性:\n');
fprintf('✓ 真实时间驱动的仿真循环\n');
fprintf('✓ 异步计算和渲染处理\n');
fprintf('✓ 交互式控制界面\n');
fprintf('✓ 实时参数调整\n');
fprintf('✓ 状态机管理\n');
fprintf('✓ 键盘快捷键支持\n\n');

fprintf('控制说明:\n');
fprintf('• 界面按钮：播放/暂停、重置、速度调节\n');
fprintf('• 空格键：播放/暂停切换\n');
fprintf('• R键：重置仿真\n');
fprintf('• ESC键：停止仿真\n');
fprintf('• +/-键：调整仿真速度\n\n');

try
    % 创建实时仿真引擎
    fprintf('正在创建实时仿真引擎...\n');
    engine = RealTimeSimulationEngine();
    
    % 初始化仿真参数
    fprintf('正在初始化仿真环境...\n');
    engine.initialize(InflowRate, SceStr, asStr, ...
                     'TimeScale', timeScale, ...
                     'FrameRate', frameRate, ...
                     'SimStepRate', simStepRate);
    
    fprintf('实时仿真引擎创建成功！\n\n');
    
    % 显示使用提示
    if ~autoStart
        fprintf('仿真引擎已准备就绪，请使用以下方式开始仿真:\n');
        fprintf('1. 点击界面上的"▶ 开始"按钮\n');
        fprintf('2. 按空格键\n');
        fprintf('3. 在命令行中输入: engine.start()\n\n');
        
        fprintf('您也可以通过以下命令控制仿真:\n');
        fprintf('engine.start()   - 开始仿真\n');
        fprintf('engine.pause()   - 暂停仿真\n');
        fprintf('engine.resume()  - 继续仿真\n');
        fprintf('engine.stop()    - 停止仿真\n');
        fprintf('engine.reset()   - 重置仿真\n\n');
    else
        fprintf('自动开始仿真...\n');
        engine.start();
    end
    
    % 显示性能提示
    fprintf('性能提示:\n');
    if frameRate > 30 || simStepRate > 25
        fprintf('⚠ 当前设置为高性能模式，如果出现卡顿请降低帧率或仿真频率\n');
    elseif frameRate < 15 || simStepRate < 10
        fprintf('ℹ 当前设置为节能模式，适合长时间仿真或低性能设备\n');
    else
        fprintf('✓ 当前设置为平衡模式，适合大多数使用场景\n');
    end
    
    fprintf('\n仿真引擎启动完成！享受真正的实时仿真体验！\n');
    fprintf('========================================\n\n');
    
catch ME
    fprintf('\n❌ 实时仿真引擎启动失败:\n');
    fprintf('错误信息: %s\n', ME.message);
    if ~isempty(ME.stack)
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    fprintf('\n请检查:\n');
    fprintf('1. MATLAB版本是否支持timer对象\n');
    fprintf('2. 图形界面是否可用\n');
    fprintf('3. 仿真参数是否有效\n');
    fprintf('4. 相关函数文件是否存在\n\n');
    
    rethrow(ME);
end

end
