% RunLAATSim - Executes the LAAT simulation with specified parameters.
%
% Syntax:
%   RunLAATSim(InflowRate, SceStr, asStr, varargin)
%
% Description:
%   This function runs the LAAT simulation using the provided inflow rate,
%   scenario string, and additional settings string. It serves as the main
%   entry point for configuring and executing the simulation.
%
% Inputs:
%   InflowRate - Numeric value specifying the inflow rate for the simulation.
%   SceStr     - String specifying the scenario to simulate.
%   asStr      - String specifying additional settings or parameters.
%   varargin   - Optional parameters:
%                'RealTimeVis', true/false - Enable real-time visualization (default: false)
%                'VisUpdateRate', rate - Visualization update rate in Hz (default: 10)
%
% Outputs:
%   None.
%
% Examples:
%   RunLAATSim(10/60, 'Scenario1', 'NYC');  % 标准仿真
%   RunLAATSim(10/60, 'Scenario1', 'NYC', 'RealTimeVis', true);  % 实时可视化
%   RunLAATSim(10/60, 'Scenario1', 'NYC', 'RealTimeVis', true, 'VisUpdateRate', 5);  % 自定义更新率
%
% Notes:
%   Ensure that the input parameters are valid and correspond to the
%   expected formats for the simulation to run correctly.
% Author: <PERSON><PERSON>
% Date Created: 2023-02-08
% Modified: 2024-09-26 - Added real-time visualization support
function [] = RunLAATSim(InflowRate,SceStr,asStr,varargin)
%%
% 解析可选参数
p = inputParser;
addParameter(p, 'RealTimeVis', false, @islogical);
addParameter(p, 'VisUpdateRate', 10, @isnumeric);
parse(p, varargin{:});

% 获取参数
enableRealTimeVis = p.Results.RealTimeVis;
visUpdateRate = p.Results.VisUpdateRate;

clc; close all; dbstop if error;
close all force; close all hidden;

% 根据是否启用实时可视化来决定是否显示进度条
if ~enableRealTimeVis
    fwaitbar = waitbar(0,'Starting Simulation');
else
    fwaitbar = [];
    disp('=== 启用实时可视化模式 ===');
    disp(['可视化更新频率: ' num2str(visUpdateRate) ' Hz']);
end

UIRun = 0;
SimInfo.RT.TCP_PostRunningTime = [];
SimInfo.RT.TFCRunningTime = [];
SimInfo.RT.SimStartTime = datetime;
disp(['Simuation started: Time=' datestr(SimInfo.RT.SimStartTime,'yyyy-mm-dd HH:MM:SS.FFF')])
SimFilename = [SceStr];
SimInfo.SimOutputDirStr = ['.\Outputs\SimOutput_' datestr(now,'yyyymmdd_hhMMss') '_' SimFilename '\'];
if ~exist(SimInfo.SimOutputDirStr, 'dir')
    mkdir(SimInfo.SimOutputDirStr)
end
%% Settings
if ~isempty(fwaitbar)
    waitbar(0,fwaitbar,'Determining Setting');
end
disp('正在配置仿真参数...');
[Settings.Airspace] = SettingAirspace(1500,1500,90,asStr,UIRun); % 20*60*30/3.6,20*60*30/3.6
[Settings.Aircraft] = SettingAircraft([20,20],[10,10]);
[Settings.Sim] = SettingSimulation(InflowRate,10);

%% Init Objects
SimInfo.Mina = []; SimInfo.Mque = []; SimInfo.Mact = []; SimInfo.Marr = []; SimInfo.MactBQ = [];
SimInfo.M = 1:1:Settings.Sim.M; SimInfo.cc = 0;
dtS = Settings.Sim.dtsim; dtM = Settings.Sim.dtMFD; tf = Settings.Sim.tf;
SimInfo.dtS = dtS; SimInfo.dtM = dtM; SimInfo.tf = tf;
SimInfo.pdt = (zeros((SimInfo.tf/SimInfo.dtS)+1,3*size(SimInfo.M,2))); SimInfo.vdt = (zeros((SimInfo.tf/SimInfo.dtS)+1,3*size(SimInfo.M,2))); SimInfo.statusdt = (zeros((SimInfo.tf/SimInfo.dtS)+1,size(SimInfo.M,2))); SimInfo.ridt = (zeros((SimInfo.tf/SimInfo.dtS)+1,size(SimInfo.M,2)));
TFC = []; TFC.CS = []; TFC.EC = [];
TFC.EC.ECdt = zeros((SimInfo.tf/SimInfo.dtS)+1,size(SimInfo.M,2)); TFC.EC.sumECtdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.sumECqdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.avgECtdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.avgECqdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.sumECdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1);

%% Aircraft Creation
if ~isempty(fwaitbar)
    waitbar(0,fwaitbar,'Initalizing Aircraft');
end
disp('正在初始化飞行器对象...');
[SimInfo,ObjAircraft] = InitAircraftObj(SimInfo,Settings);

%% 初始化实时可视化
hFig = []; hAxes = [];
if enableRealTimeVis
    disp('正在初始化实时可视化界面...');
    try
        [hFig, hAxes] = RealTimeVisualization('init', Settings);
        disp('实时可视化界面初始化成功！');
    catch ME
        warning('实时可视化初始化失败: %s', ME.message);
        enableRealTimeVis = false;
    end
end

%% Export Settings
if ~isempty(fwaitbar)
    close(fwaitbar)
    fwaitbar = waitbar(0,'Initalizing Aircraft');
end
%% Simulation
% Start Simulation
disp('开始仿真循环...');
if enableRealTimeVis
    disp('实时可视化已启用 - 您可以观察飞行器的实时运动');
end

for t=0:dtS:tf
    % 更新进度显示
    if ~isempty(fwaitbar)
        waitbar(t/tf,fwaitbar,{['Running Simulation  [t=' sprintf('%0.1f',t) '/' sprintf('%0.0f',tf) ']']});
    else
        % 在实时可视化模式下，每隔一定时间显示进度
        if mod(t, 10) < dtS  % 每10秒显示一次进度
            fprintf('仿真进度: %.1f%% (t=%.1f/%.0f s)\n', 100*t/tf, t, tf);
        end
    end

    SimInfo.t = t;

    %% Departures
    [SimInfo,ObjAircraft] = AircraftDepartures(SimInfo,ObjAircraft);

    %% Controller + Motion
    [SimInfo,ObjAircraft] = AircraftController(SimInfo,ObjAircraft,Settings);

    %% Arrival
    [SimInfo,ObjAircraft] = AircraftArrivals(SimInfo,ObjAircraft);

    %% Update SimInfo
    [SimInfo] = UpdateSimInfo(SimInfo,ObjAircraft);

    %% Energy Conspution
    if (t~=0)
        [TFC.EC,ObjAircraft] = CalEC_AG(TFC.EC,SimInfo,ObjAircraft);
    end

    %% TFC
    if (t~=0)&&(mod(t,dtM)==0)
        SimInfo.RT.TFCStartTime = datetime;
        [TFC] = CalTFC_N(TFC,SimInfo,ObjAircraft,Settings);
        [TFC] = CalTFC_Ri(TFC,SimInfo,ObjAircraft,Settings);
        SimInfo.RT.TFCEndTime = datetime;
        SimInfo.RT.TFCRunningTime(end+1) = seconds(SimInfo.RT.TFCEndTime-SimInfo.RT.TFCStartTime);
    end

    %% 实时可视化更新
    if enableRealTimeVis && ~isempty(hFig) && isvalid(hFig)
        try
            % 控制更新频率：每隔 1/visUpdateRate 秒更新一次
            if mod(t, 1/visUpdateRate) < dtS
                RealTimeVisualization('update', hFig, hAxes, t, SimInfo, ObjAircraft, TFC, Settings);
            end
        catch ME
            warning('实时可视化更新失败: %s', ME.message);
            % 如果可视化出错，继续仿真但禁用可视化
            enableRealTimeVis = false;
        end
    end

    % 检查用户是否关闭了可视化窗口
    if enableRealTimeVis && (~isempty(hFig) && ~isvalid(hFig))
        disp('检测到可视化窗口已关闭，继续仿真但禁用实时显示...');
        enableRealTimeVis = false;
        hFig = [];
        hAxes = [];
    end
end
%% 仿真结束处理
if ~isempty(fwaitbar)
    waitbar(1,fwaitbar,'Finishing Simulation');
end

disp('仿真循环完成！');

% 清理实时可视化
if enableRealTimeVis && ~isempty(hFig) && isvalid(hFig)
    disp('保持可视化窗口打开，您可以查看最终结果...');
    disp('关闭可视化窗口以继续执行后续处理...');

    % 等待用户关闭窗口或者设置超时
    timeout = 30; % 30秒超时
    start_time = tic;
    while isvalid(hFig) && toc(start_time) < timeout
        pause(0.5);
    end

    if isvalid(hFig)
        disp('超时，自动关闭可视化窗口...');
        RealTimeVisualization('close', hFig);
    end
end

clear t dtS dtM dtC tf
SimInfo.RT.SimEndTime = datetime;
SimInfo.RT.SimRunningTime = seconds(SimInfo.RT.SimEndTime-SimInfo.RT.SimStartTime);
SimInfo.RT.SimRunningTimeStr = datestr(SimInfo.RT.SimEndTime-SimInfo.RT.SimStartTime,'HH:MM:SS.FFF');

disp(['Simuation Ended: Time=' datestr(SimInfo.RT.SimEndTime,'yyyy-mm-dd HH:MM:SS.FFF')])
disp(['TFC RunningTime: Time=' num2str(sum(SimInfo.RT.TFCRunningTime)) ' [seconds]'])
disp(['TCP_Post RunningTime: Time=' num2str(sum(SimInfo.RT.TCP_PostRunningTime)) ' [seconds]'])

if ~isempty(fwaitbar)
    waitbar(1,fwaitbar,'Exporting Data');
end

%% Exporting and Plotting
% Export Workspace
if ~isempty(fwaitbar)
    close(fwaitbar)
end

disp('正在导出仿真数据...');
ExportJSON(['../public/Outputs/' 'SimOutput_' SimFilename],SimInfo,ObjAircraft,TFC,Settings)
save([SimInfo.SimOutputDirStr 'Trajectories' '_' SimFilename],'-v7.3'); clear SimFilename;

if ~enableRealTimeVis  % 只有在没有实时可视化时才生成视频
    if ~isempty(fwaitbar)
        fwaitbar = waitbar(1,'Generating Motion Picture...');
    end
    disp('正在生成运动轨迹视频...');

    % Export Video
    try
        %PlotMotionPicture(60,SimInfo,ObjAircraft,TFC,Settings);
        PlotMotionPicture_3DMotion(60,SimInfo,ObjAircraft,TFC,Settings);
        disp('轨迹视频生成完成！');
    catch ME
        warning('视频生成失败: %s', ME.message);
    end

    if ~isempty(fwaitbar)
        waitbar(1,fwaitbar,'Done');
        pause(0.1)
        close(fwaitbar)
    end
else
    disp('实时可视化模式：跳过视频生成以节省时间');
end

clear fwaitbar;
disp('=== 仿真完成 ===');
end