function DemoRealTimeSimulation()
% DemoRealTimeSimulation - 实时仿真引擎演示程序
%
% 描述:
%   这是一个演示程序，展示如何使用新的实时LAAT仿真引擎。
%   提供多个预设场景，让用户快速体验真正的实时仿真功能。
%
% 使用方法:
%   直接运行: DemoRealTimeSimulation()
%   然后从菜单中选择演示场景
%
% 演示场景:
%   1. 基础实时仿真 - 标准参数，手动控制
%   2. 快速仿真演示 - 2倍速，自动开始
%   3. 慢动作分析 - 0.5倍速，详细观察
%   4. 高密度交通 - 大流量仿真
%   5. 多场景对比 - 不同城市场景
%   6. 性能测试 - 高帧率高频率
%
% 作者: AI Assistant
% 创建日期: 2024-09-26

clc;
fprintf('\n');
fprintf('╔══════════════════════════════════════════════════════════════╗\n');
fprintf('║                    实时LAAT仿真引擎演示                      ║\n');
fprintf('║                  Real-Time Simulation Demo                  ║\n');
fprintf('╚══════════════════════════════════════════════════════════════╝\n');
fprintf('\n');

fprintf('欢迎使用真正的实时LAAT仿真引擎！\n');
fprintf('这是一个基于事件驱动架构的全新仿真系统，提供:\n\n');
fprintf('🚁 真实时间驱动的仿真循环\n');
fprintf('⚡ 异步计算和渲染处理\n');
fprintf('🎮 交互式控制界面\n');
fprintf('📊 实时数据可视化\n');
fprintf('⌨️  键盘快捷键支持\n');
fprintf('🔧 实时参数调整\n\n');

% 显示演示选项
fprintf('请选择演示场景:\n');
fprintf('─────────────────────────────────────────────────────────────\n');
fprintf('1. 🎯 基础实时仿真    - 标准参数，手动控制体验\n');
fprintf('2. ⚡ 快速仿真演示    - 2倍速自动仿真，观察加速效果\n');
fprintf('3. 🔍 慢动作分析      - 0.5倍速详细观察飞行器行为\n');
fprintf('4. 🚦 高密度交通      - 大流量仿真，测试系统性能\n');
fprintf('5. 🌍 多场景对比      - 体验不同城市空域的差异\n');
fprintf('6. 🏎️  性能测试        - 高帧率高频率极限测试\n');
fprintf('7. 📚 查看使用说明    - 详细的操作指南\n');
fprintf('8. ❌ 退出程序\n');
fprintf('─────────────────────────────────────────────────────────────\n');

while true
    choice = input('请输入选择 (1-8): ');
    
    if isempty(choice) || ~isnumeric(choice) || choice < 1 || choice > 8
        fprintf('⚠️  无效选择，请输入1-8之间的数字\n');
        continue;
    end
    
    switch choice
        case 1
            runBasicDemo();
            break;
        case 2
            runFastDemo();
            break;
        case 3
            runSlowMotionDemo();
            break;
        case 4
            runHighDensityDemo();
            break;
        case 5
            runMultiScenarioDemo();
            break;
        case 6
            runPerformanceTest();
            break;
        case 7
            showUserGuide();
            continue;
        case 8
            fprintf('\n👋 感谢使用实时LAAT仿真引擎演示程序！\n');
            return;
    end
end

end

function runBasicDemo()
    fprintf('\n🎯 启动基础实时仿真演示...\n');
    fprintf('参数: 流入率 10架/分钟, 纽约场景, 实时速度\n');
    fprintf('特点: 手动控制，适合初次体验\n\n');
    
    try
        engine = RunLAATSimRealTimeEngine(10/60, 'BasicDemo', 'NYC');
        fprintf('✅ 基础演示启动成功！请使用界面控制仿真。\n');
    catch ME
        fprintf('❌ 演示启动失败: %s\n', ME.message);
    end
end

function runFastDemo()
    fprintf('\n⚡ 启动快速仿真演示...\n');
    fprintf('参数: 流入率 15架/分钟, 旧金山场景, 2倍速度\n');
    fprintf('特点: 自动开始，快速观察仿真过程\n\n');
    
    try
        engine = RunLAATSimRealTimeEngine(15/60, 'FastDemo', 'SF', ...
                                         'TimeScale', 2.0, 'AutoStart', true);
        fprintf('✅ 快速演示启动成功！仿真正在2倍速运行。\n');
    catch ME
        fprintf('❌ 演示启动失败: %s\n', ME.message);
    end
end

function runSlowMotionDemo()
    fprintf('\n🔍 启动慢动作分析演示...\n');
    fprintf('参数: 流入率 8架/分钟, 巴黎场景, 0.5倍速度\n');
    fprintf('特点: 慢速播放，便于详细观察飞行器行为\n\n');
    
    try
        engine = RunLAATSimRealTimeEngine(8/60, 'SlowMotion', 'PAR', ...
                                         'TimeScale', 0.5);
        fprintf('✅ 慢动作演示启动成功！请观察飞行器的详细行为。\n');
    catch ME
        fprintf('❌ 演示启动失败: %s\n', ME.message);
    end
end

function runHighDensityDemo()
    fprintf('\n🚦 启动高密度交通演示...\n');
    fprintf('参数: 流入率 25架/分钟, 纽约场景, 1.5倍速度\n');
    fprintf('特点: 大流量仿真，测试避碰算法和系统性能\n\n');
    
    try
        engine = RunLAATSimRealTimeEngine(25/60, 'HighDensity', 'NYC', ...
                                         'TimeScale', 1.5, 'AutoStart', true);
        fprintf('✅ 高密度演示启动成功！观察大流量下的交通管理。\n');
    catch ME
        fprintf('❌ 演示启动失败: %s\n', ME.message);
    end
end

function runMultiScenarioDemo()
    fprintf('\n🌍 多场景对比演示\n');
    fprintf('将依次启动不同城市场景的仿真，便于对比观察\n\n');
    
    scenarios = {'NYC', 'SF', 'PAR'};
    names = {'纽约', '旧金山', '巴黎'};
    
    for i = 1:length(scenarios)
        fprintf('正在启动 %s 场景...\n', names{i});
        try
            engine = RunLAATSimRealTimeEngine(12/60, ['MultiDemo_' scenarios{i}], scenarios{i});
            fprintf('✅ %s 场景启动成功！\n', names{i});
            
            if i < length(scenarios)
                input('按回车键继续下一个场景...');
            end
        catch ME
            fprintf('❌ %s 场景启动失败: %s\n', names{i}, ME.message);
        end
    end
end

function runPerformanceTest()
    fprintf('\n🏎️ 启动性能测试...\n');
    fprintf('参数: 流入率 20架/分钟, 纽约场景, 60fps渲染, 50Hz仿真\n');
    fprintf('特点: 极限性能测试，适合录制高质量视频\n');
    fprintf('⚠️  注意: 此模式对系统性能要求较高\n\n');
    
    try
        engine = RunLAATSimRealTimeEngine(20/60, 'PerfTest', 'NYC', ...
                                         'FrameRate', 60, 'SimStepRate', 50, ...
                                         'AutoStart', true);
        fprintf('✅ 性能测试启动成功！监控FPS显示以评估性能。\n');
    catch ME
        fprintf('❌ 性能测试启动失败: %s\n', ME.message);
    end
end

function showUserGuide()
    fprintf('\n📚 实时仿真引擎使用说明\n');
    fprintf('═══════════════════════════════════════════════════════════\n\n');
    
    fprintf('🎮 界面控制:\n');
    fprintf('  ▶️ 播放/暂停按钮  - 控制仿真运行状态\n');
    fprintf('  ⟲ 重置按钮      - 重置仿真到初始状态\n');
    fprintf('  🎚️ 速度滑块      - 实时调整仿真速度 (0.1x - 5.0x)\n');
    fprintf('  ⏱️ 时间滑块      - 跳转到指定时间点\n\n');
    
    fprintf('⌨️ 键盘快捷键:\n');
    fprintf('  空格键    - 播放/暂停切换\n');
    fprintf('  R键       - 重置仿真\n');
    fprintf('  ESC键     - 停止仿真\n');
    fprintf('  +键       - 增加仿真速度\n');
    fprintf('  -键       - 减少仿真速度\n\n');
    
    fprintf('💻 程序化控制:\n');
    fprintf('  engine.start()   - 开始仿真\n');
    fprintf('  engine.pause()   - 暂停仿真\n');
    fprintf('  engine.resume()  - 继续仿真\n');
    fprintf('  engine.stop()    - 停止仿真\n');
    fprintf('  engine.reset()   - 重置仿真\n\n');
    
    fprintf('📊 信息面板:\n');
    fprintf('  • 仿真状态 - 当前运行状态和时间信息\n');
    fprintf('  • 飞行器统计 - 活跃、到达、排队数量\n');
    fprintf('  • 交通流信息 - 密度和平均速度\n');
    fprintf('  • 能耗统计 - 总能耗和平均能耗\n');
    fprintf('  • 性能监控 - 渲染FPS和仿真频率\n\n');
    
    fprintf('🎨 可视化特性:\n');
    fprintf('  • 实时3D飞行器位置显示\n');
    fprintf('  • 历史轨迹跟踪\n');
    fprintf('  • 目标点和路径连线\n');
    fprintf('  • 空域边界和区域网格\n');
    fprintf('  • 不同类型飞行器颜色区分\n\n');
    
    fprintf('⚡ 性能建议:\n');
    fprintf('  小规模 (<50架):   30fps渲染, 20Hz仿真\n');
    fprintf('  中等规模 (50-100): 20fps渲染, 15Hz仿真\n');
    fprintf('  大规模 (>100架):   15fps渲染, 10Hz仿真\n\n');
    
    fprintf('═══════════════════════════════════════════════════════════\n');
    input('按回车键返回主菜单...');
end
