% RunLAATSimRealTime - 启动带实时可视化的LAAT仿真
%
% 语法:
%   RunLAATSimRealTime(InflowRate, SceStr, asStr, varargin)
%
% 描述:
%   这是RunLAATSim的便捷包装函数，自动启用实时可视化功能。
%   提供了更简单的接口来运行带有实时显示的仿真。
%
% 输入参数:
%   InflowRate - 流入率（架/秒）
%   SceStr     - 场景名称字符串
%   asStr      - 空域类型字符串
%   varargin   - 可选参数:
%                'VisUpdateRate', rate - 可视化更新频率（Hz，默认10）
%                'ShowProgress', true/false - 是否显示进度信息（默认true）
%
% 输出:
%   无
%
% 示例:
%   % 基本实时可视化仿真
%   RunLAATSimRealTime(10/60, 'Test1', 'NYC');
%   
%   % 自定义更新频率的实时仿真
%   RunLAATSimRealTime(15/60, 'HighFreq', 'SF', 'VisUpdateRate', 20);
%   
%   % 静默模式（减少控制台输出）
%   RunLAATSimRealTime(8/60, 'Quiet', 'PAR', 'ShowProgress', false);
%
% 注意:
%   - 实时可视化会略微降低仿真速度，但提供直观的观察体验
%   - 建议在小规模仿真中使用以获得最佳性能
%   - 可视化窗口关闭后仿真会继续运行
%
% 作者: AI Assistant  
% 创建日期: 2024-09-26

function RunLAATSimRealTime(InflowRate, SceStr, asStr, varargin)

% 解析输入参数
p = inputParser;
addRequired(p, 'InflowRate', @isnumeric);
addRequired(p, 'SceStr', @ischar);
addRequired(p, 'asStr', @ischar);
addParameter(p, 'VisUpdateRate', 10, @isnumeric);
addParameter(p, 'ShowProgress', true, @islogical);

parse(p, InflowRate, SceStr, asStr, varargin{:});

% 获取参数
visUpdateRate = p.Results.VisUpdateRate;
showProgress = p.Results.ShowProgress;

% 显示启动信息
if showProgress
    fprintf('\n=== LAAT仿真实时可视化模式 ===\n');
    fprintf('场景: %s\n', SceStr);
    fprintf('空域: %s\n', asStr);
    fprintf('流入率: %.3f 架/秒 (%.1f 架/分钟)\n', InflowRate, InflowRate*60);
    fprintf('可视化更新频率: %d Hz\n', visUpdateRate);
    fprintf('=====================================\n\n');
    
    fprintf('提示:\n');
    fprintf('- 仿真开始后将打开实时可视化窗口\n');
    fprintf('- 您可以观察飞行器的实时运动和统计信息\n');
    fprintf('- 关闭可视化窗口不会停止仿真\n');
    fprintf('- 实时模式会跳过最终视频生成以节省时间\n\n');
    
    fprintf('正在启动仿真...\n');
end

% 调用主仿真函数，启用实时可视化
try
    RunLAATSim(InflowRate, SceStr, asStr, ...
               'RealTimeVis', true, ...
               'VisUpdateRate', visUpdateRate);
    
    if showProgress
        fprintf('\n仿真成功完成！\n');
    end
    
catch ME
    if showProgress
        fprintf('\n仿真过程中发生错误:\n');
        fprintf('错误信息: %s\n', ME.message);
        fprintf('错误位置: %s (第%d行)\n', ME.stack(1).name, ME.stack(1).line);
    end
    rethrow(ME);
end

end
