% RealTimeVisualization - 实时可视化仿真过程
%
% 语法:
%   [hFig, hAxes] = RealTimeVisualization(action, varargin)
%
% 输入参数:
%   action - 操作类型: 'init', 'update', 'close'
%   varargin - 根据action不同而变化的参数
%
% 输出参数:
%   hFig - 图形窗口句柄
%   hAxes - 坐标轴句柄
%
% 使用示例:
%   % 初始化
%   [hFig, hAxes] = RealTimeVisualization('init', Settings);
%   % 更新显示
%   RealTimeVisualization('update', hFig, hAxes, t, SimInfo, ObjAircraft, TFC, Settings);
%   % 关闭
%   RealTimeVisualization('close', hFig);
%
% 作者: AI Assistant
% 创建日期: 2024-09-26

function [hFig, hAxes] = RealTimeVisualization(action, varargin)

persistent figHandle axesHandle airspaceDrawn lastUpdateTime updateInterval;

switch lower(action)
    case 'init'
        % 初始化可视化窗口
        Settings = varargin{1};
        
        % 设置更新间隔（秒）
        updateInterval = 0.1; % 每0.1秒更新一次，避免过于频繁
        lastUpdateTime = 0;
        
        % 创建图形窗口
        figHandle = figure('Name', 'LAAT仿真实时可视化', ...
                          'NumberTitle', 'off', ...
                          'Position', [100, 100, 1200, 800], ...
                          'Color', 'white', ...
                          'CloseRequestFcn', @closeCallback);
        
        % 创建主坐标轴
        axesHandle = subplot(2, 2, [1, 3]); % 占据左侧大部分空间
        hold(axesHandle, 'on');
        grid(axesHandle, 'on');
        axis(axesHandle, 'equal');
        
        % 设置坐标轴属性
        AirspaceS = Settings.Airspace;
        AxisSize = 1.2 * max([AirspaceS.dx, AirspaceS.dy, AirspaceS.dz]);
        axis(axesHandle, [-AxisSize/2, AxisSize/2, -AxisSize/2, AxisSize/2, 0, AxisSize]);
        
        xlabel(axesHandle, 'X [m]', 'FontSize', 12);
        ylabel(axesHandle, 'Y [m]', 'FontSize', 12);
        zlabel(axesHandle, 'Z [m]', 'FontSize', 12);
        title(axesHandle, '低空空域交通仿真 - 实时视图', 'FontSize', 14, 'FontWeight', 'bold');
        
        % 设置视角
        view(axesHandle, [45, 30]);
        
        % 绘制空域边界（只需绘制一次）
        drawAirspace(axesHandle, Settings.Airspace);
        airspaceDrawn = true;
        
        % 创建统计信息显示区域
        createInfoPanels(figHandle);
        
        % 返回句柄
        hFig = figHandle;
        hAxes = axesHandle;
        
    case 'update'
        % 更新显示
        if nargin < 7
            error('更新模式需要7个参数: hFig, hAxes, t, SimInfo, ObjAircraft, TFC, Settings');
        end
        
        hFig = varargin{1};
        hAxes = varargin{2};
        t = varargin{3};
        SimInfo = varargin{4};
        ObjAircraft = varargin{5};
        TFC = varargin{6};
        Settings = varargin{7};
        
        % 检查是否需要更新（控制更新频率）
        if t - lastUpdateTime < updateInterval
            return;
        end
        lastUpdateTime = t;
        
        % 检查图形窗口是否仍然存在
        if ~isvalid(hFig)
            return;
        end
        
        % 清除之前的飞行器绘图（保留空域）
        children = get(hAxes, 'Children');
        % 删除飞行器相关的图形对象（保留空域边界）
        for i = length(children):-1:1
            if isprop(children(i), 'Tag') && strcmp(get(children(i), 'Tag'), 'aircraft')
                delete(children(i));
            end
        end
        
        % 绘制当前活跃的飞行器
        drawAircraft(hAxes, t, SimInfo, ObjAircraft, Settings);
        
        % 更新统计信息
        updateInfoPanels(hFig, t, SimInfo, ObjAircraft, TFC, Settings);
        
        % 强制刷新显示
        drawnow limitrate;
        
        % 返回句柄
        hFig = figHandle;
        hAxes = axesHandle;
        
    case 'close'
        % 关闭可视化窗口
        if nargin >= 2
            hFig = varargin{1};
            if isvalid(hFig)
                close(hFig);
            end
        end
        
        % 清理持久变量
        clear figHandle axesHandle airspaceDrawn lastUpdateTime updateInterval;
        
        hFig = [];
        hAxes = [];
        
    otherwise
        error('未知的操作类型: %s', action);
end

end

%% 辅助函数

function drawAirspace(hAxes, AirspaceS)
    % 绘制空域边界和区域
    
    % 主空域边界
    drawCube(hAxes, AirspaceS.xyz, 'none', 0.1, 'black', 2, '-');
    
    % VTOL区域边界
    if AirspaceS.VTOL
        drawCube(hAxes, AirspaceS.VTOLxyz, 'none', 0.05, 'blue', 1, '--');
    end
    
    % 绘制区域网格（简化版）
    if isfield(AirspaceS, 'Regions') && AirspaceS.Regions.n > 0
        for ri = 1:min(AirspaceS.Regions.n, 20) % 限制绘制数量以提高性能
            if mod(ri, 3) == 1 % 只绘制部分区域以避免过于复杂
                drawCube(hAxes, AirspaceS.Regions.B(ri).xyz, 'none', 0.02, 'gray', 0.5, ':');
            end
        end
    end
end

function drawCube(hAxes, xyz, faceColor, faceAlpha, edgeColor, lineWidth, lineStyle)
    % 绘制立方体
    point1 = xyz(1,:);
    point2 = xyz(2,:);
    center = (point1 + point2) / 2;
    dxyz = abs(point2 - point1);
    
    % 定义立方体的8个顶点
    vertices = [
        center + [ dxyz(1)/2,  dxyz(2)/2,  dxyz(3)/2];
        center + [-dxyz(1)/2,  dxyz(2)/2,  dxyz(3)/2];
        center + [-dxyz(1)/2, -dxyz(2)/2,  dxyz(3)/2];
        center + [ dxyz(1)/2, -dxyz(2)/2,  dxyz(3)/2];
        center + [ dxyz(1)/2,  dxyz(2)/2, -dxyz(3)/2];
        center + [-dxyz(1)/2,  dxyz(2)/2, -dxyz(3)/2];
        center + [-dxyz(1)/2, -dxyz(2)/2, -dxyz(3)/2];
        center + [ dxyz(1)/2, -dxyz(2)/2, -dxyz(3)/2];
    ];
    
    % 定义立方体的6个面
    faces = [1 2 3 4; 2 6 7 3; 4 3 7 8; 1 5 8 4; 1 2 6 5; 5 6 7 8];
    
    % 绘制立方体
    patch('Parent', hAxes, 'Vertices', vertices, 'Faces', faces, ...
          'FaceColor', faceColor, 'FaceAlpha', faceAlpha, ...
          'EdgeColor', edgeColor, 'LineWidth', lineWidth, 'LineStyle', lineStyle);
end

function drawAircraft(hAxes, t, SimInfo, ObjAircraft, Settings)
    % 绘制飞行器当前位置和轨迹
    
    dtS = SimInfo.dtS;
    current_step = round(t/dtS) + 1;
    
    % 获取当前活跃的飞行器
    active_aircraft = SimInfo.Mact;
    
    % 定义飞行器类型颜色
    aircraft_colors = [
        1.0, 0.0, 0.0;  % EPAV - 红色
        0.0, 1.0, 0.0;  % EUAV - 绿色  
        0.0, 0.0, 1.0;  % PAV - 蓝色
        1.0, 0.5, 0.0;  % UAV - 橙色
    ];
    
    for i = 1:length(active_aircraft)
        aa = active_aircraft(i);
        
        if aa > length(ObjAircraft) || current_step > size(SimInfo.pdt, 1)
            continue;
        end
        
        % 获取飞行器当前位置
        x_current = SimInfo.pdt(current_step, 3*aa-2);
        y_current = SimInfo.pdt(current_step, 3*aa-1);
        z_current = SimInfo.pdt(current_step, 3*aa);
        
        % 跳过无效位置
        if x_current == 0 && y_current == 0 && z_current == 0
            continue;
        end
        
        % 获取飞行器类型和颜色
        aircraft_type = ObjAircraft(aa).AMI;
        color = aircraft_colors(min(aircraft_type, 4), :);
        
        % 绘制飞行器当前位置（大圆点）
        plot3(hAxes, x_current, y_current, z_current, 'o', ...
              'Color', color, 'MarkerSize', 8, 'MarkerFaceColor', color, ...
              'Tag', 'aircraft');
        
        % 绘制历史轨迹
        if current_step > 1
            x_history = SimInfo.pdt(1:current_step, 3*aa-2);
            y_history = SimInfo.pdt(1:current_step, 3*aa-1);
            z_history = SimInfo.pdt(1:current_step, 3*aa);
            
            % 移除零值点
            valid_points = ~(x_history == 0 & y_history == 0 & z_history == 0);
            if sum(valid_points) > 1
                plot3(hAxes, x_history(valid_points), y_history(valid_points), z_history(valid_points), ...
                      '-', 'Color', [color, 0.6], 'LineWidth', 1.5, 'Tag', 'aircraft');
            end
        end
        
        % 绘制目标点
        if isfield(ObjAircraft(aa), 'd') && ~isempty(ObjAircraft(aa).d)
            plot3(hAxes, ObjAircraft(aa).d(1), ObjAircraft(aa).d(2), ObjAircraft(aa).d(3), ...
                  'x', 'Color', color, 'MarkerSize', 6, 'LineWidth', 2, 'Tag', 'aircraft');
            
            % 绘制到目标的连线
            plot3(hAxes, [x_current, ObjAircraft(aa).d(1)], ...
                         [y_current, ObjAircraft(aa).d(2)], ...
                         [z_current, ObjAircraft(aa).d(3)], ...
                  '--', 'Color', [color, 0.3], 'LineWidth', 1, 'Tag', 'aircraft');
        end
        
        % 绘制安全半径（可选，仅对少数飞行器显示以避免混乱）
        if i <= 3 && isfield(ObjAircraft(aa), 'rs')
            drawSphere(hAxes, x_current, y_current, z_current, ObjAircraft(aa).rs, color, 0.1);
        end
    end
end

function drawSphere(hAxes, x, y, z, radius, color, alpha)
    % 绘制球体（简化版，使用圆圈表示）
    theta = linspace(0, 2*pi, 20);
    x_circle = x + radius * cos(theta);
    y_circle = y + radius * sin(theta);
    z_circle = z * ones(size(theta));
    
    plot3(hAxes, x_circle, y_circle, z_circle, '-', 'Color', [color, alpha], ...
          'LineWidth', 1, 'Tag', 'aircraft');
end

function createInfoPanels(hFig)
    % 创建信息显示面板
    
    % 时间信息面板
    uicontrol('Parent', hFig, 'Style', 'text', ...
              'Position', [850, 700, 300, 80], ...
              'String', '仿真时间: 0.0 s', ...
              'FontSize', 14, 'FontWeight', 'bold', ...
              'BackgroundColor', 'white', 'Tag', 'timeInfo');
    
    % 飞行器统计面板
    uicontrol('Parent', hFig, 'Style', 'text', ...
              'Position', [850, 600, 300, 80], ...
              'String', '活跃飞行器: 0', ...
              'FontSize', 12, ...
              'BackgroundColor', 'white', 'Tag', 'aircraftInfo');
    
    % 交通流信息面板
    uicontrol('Parent', hFig, 'Style', 'text', ...
              'Position', [850, 500, 300, 80], ...
              'String', '交通流密度: N/A', ...
              'FontSize', 12, ...
              'BackgroundColor', 'white', 'Tag', 'trafficInfo');
    
    % 能耗信息面板
    uicontrol('Parent', hFig, 'Style', 'text', ...
              'Position', [850, 400, 300, 80], ...
              'String', '总能耗: N/A', ...
              'FontSize', 12, ...
              'BackgroundColor', 'white', 'Tag', 'energyInfo');
end

function updateInfoPanels(hFig, t, SimInfo, ObjAircraft, TFC, Settings)
    % 更新信息显示面板
    
    % 更新时间信息
    timeHandle = findobj(hFig, 'Tag', 'timeInfo');
    if ~isempty(timeHandle)
        set(timeHandle, 'String', sprintf('仿真时间: %.1f s\n总时长: %.1f s', t, Settings.Sim.tf));
    end
    
    % 更新飞行器统计
    aircraftHandle = findobj(hFig, 'Tag', 'aircraftInfo');
    if ~isempty(aircraftHandle)
        active_count = length(SimInfo.Mact);
        arrived_count = length(SimInfo.Marr);
        queued_count = length(SimInfo.Mque);
        
        info_str = sprintf('活跃飞行器: %d\n已到达: %d\n排队中: %d', ...
                          active_count, arrived_count, queued_count);
        set(aircraftHandle, 'String', info_str);
    end
    
    % 更新交通流信息
    trafficHandle = findobj(hFig, 'Tag', 'trafficInfo');
    if ~isempty(trafficHandle) && ~isempty(TFC) && isfield(TFC, 'N')
        if ~isempty(TFC.N) && isfield(TFC.N, 'n') && ~isempty(TFC.N.n)
            current_density = TFC.N.n(end);
            info_str = sprintf('交通流密度: %.2f\n平均速度: %.1f m/s', ...
                              current_density, 20); % 简化的平均速度
        else
            info_str = '交通流密度: 计算中...';
        end
        set(trafficHandle, 'String', info_str);
    end
    
    % 更新能耗信息
    energyHandle = findobj(hFig, 'Tag', 'energyInfo');
    if ~isempty(energyHandle) && ~isempty(TFC) && isfield(TFC, 'EC')
        if ~isempty(TFC.EC) && isfield(TFC.EC, 'sumECdt') && ~isempty(TFC.EC.sumECdt)
            total_energy = TFC.EC.sumECdt(end);
            info_str = sprintf('总能耗: %.2f Wh\n平均能耗: %.2f Wh', ...
                              total_energy, total_energy/max(1, length(SimInfo.Mact)));
        else
            info_str = '总能耗: 计算中...';
        end
        set(energyHandle, 'String', info_str);
    end
end

function closeCallback(src, ~)
    % 窗口关闭回调函数
    delete(src);
end
