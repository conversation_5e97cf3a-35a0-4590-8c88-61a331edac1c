# pyLAATSimV0200 - 低空空域交通仿真系统

## 项目概述

pyLAATSimV0200 是一个基于 MATLAB 的低空空域交通（Low Altitude Air Traffic, LAAT）仿真系统，专门用于城市空中交通（Urban Air Mobility, UAM）和垂直起降飞行器（VTOL）的交通流仿真研究。该系统能够模拟多架飞行器在三维空域中的运动、避碰、能耗计算和交通流分析。

### 主要特性

- **多飞行器仿真**：支持同时仿真多架不同类型的飞行器（PAV、UAV、EPAV、EUAV）
- **三维空域建模**：完整的三维空域环境，支持垂直起降和巡航飞行
- **智能避碰系统**：基于人工势场的分布式避碰算法
- **能耗计算**：详细的飞行器能耗模型和电池管理系统
- **交通流分析**：实时计算交通流密度、速度和通行能力
- **可视化输出**：生成飞行轨迹动画和仿真结果图表
- **多场景支持**：支持纽约、旧金山、巴黎、香港等多个城市场景

### 技术架构

系统采用模块化设计，主要包含以下核心模块：

- **仿真控制模块**：`RunLAATSim.m`、`RunLAATSimUI.m`
- **飞行器管理模块**：`AircraftController.m`、`AircraftMotion.m`、`AircraftDepartures.m`、`AircraftArrivals.m`
- **空域配置模块**：`SettingAirspace.m`、`LoadVertiports.m`、`LoadWaypoints.m`
- **交通流计算模块**：`CalTFC_N.m`、`CalTFC_Ri.m`
- **能耗计算模块**：`CalEC_AG.m`
- **可视化模块**：`PlotMotionPicture.m`、`PlotMotionPicture_3DMotion.m`
- **数据导出模块**：`ExportJSON.m`

## 系统要求

### 软件环境
- **MATLAB R2018b 或更高版本**
- **必需工具箱**：
  - Statistics and Machine Learning Toolbox
  - Optimization Toolbox
  - Parallel Computing Toolbox（可选，用于加速计算）

### 硬件要求
- **内存**：建议 8GB 以上（大规模仿真需要 16GB 以上）
- **存储空间**：至少 2GB 可用空间（用于存储仿真结果）
- **处理器**：Intel i5 或同等性能处理器

## 安装指南

### 1. 下载项目
```bash
# 克隆项目到本地
git clone <repository-url>
cd pyLAATSimV0200
```

### 2. MATLAB 环境配置
1. 启动 MATLAB
2. 将项目根目录添加到 MATLAB 路径：
   ```matlab
   addpath(genpath('path/to/pyLAATSimV0200'))
   ```
3. 验证安装：
   ```matlab
   % 检查核心函数是否可用
   which RunLAATSim
   which SettingAirspace
   ```

### 3. 数据文件准备
确保以下数据文件存在：
- `DataMAT/` 目录下的 `.mat` 文件
- `public/Vertiports/` 目录下的 JSON 配置文件（如果使用 Web 界面）
- `public/Waypoints/` 目录下的航路点文件

## 快速开始

### 基本使用示例

```matlab
% 运行一个简单的仿真场景
% 参数说明：
% - 流入率：10架/分钟 = 10/60 架/秒
% - 场景名称：'Scenario1'
% - 空域类型：'NYC'（纽约场景）
RunLAATSim(10/60, 'Scenario1', 'NYC');
```

### 实时可视化仿真 ⭐ 新功能

```matlab
% 启动带实时可视化的仿真（推荐用于观察和演示）
RunLAATSimRealTime(10/60, 'RealTimeDemo', 'NYC');

% 或者使用原函数的实时可视化参数
RunLAATSim(10/60, 'Scenario1', 'NYC', 'RealTimeVis', true);

% 自定义可视化更新频率（默认10Hz）
RunLAATSimRealTime(15/60, 'HighFreq', 'SF', 'VisUpdateRate', 20);
```

### 自定义参数仿真

```matlab
% 设置仿真参数
InflowRate = 15/60;  % 流入率：15架/分钟
SceStr = 'CustomTest';  % 场景名称
asStr = 'SF';  % 空域类型：旧金山

% 运行标准仿真
RunLAATSim(InflowRate, SceStr, asStr);

% 或运行实时可视化仿真
RunLAATSimRealTime(InflowRate, SceStr, asStr);
```

### Web 界面仿真（适用于 Flask API）

```matlab
% 定义自定义设置
NewSettings.Airspace.dx = 2000;  % X方向空域大小（米）
NewSettings.Airspace.dy = 2000;  % Y方向空域大小（米）
NewSettings.Airspace.dz = 120;   % Z方向空域高度（米）
NewSettings.Airspace.asStr = 'NYC';  % 空域类型

NewSettings.Aircraft.VmaxMin = 15;  % 最小飞行速度（m/s）
NewSettings.Aircraft.VmaxMax = 25;  % 最大飞行速度（m/s）
NewSettings.Aircraft.RsMin = 8;     % 最小安全半径（米）
NewSettings.Aircraft.RsMax = 12;    % 最大安全半径（米）

NewSettings.Sim.Qin = 20;  % 流入率（架/小时）

% 运行仿真
scenarioName = RunLAATSimUI(20/60, NewSettings, 'WebTest');
```

## 详细使用说明

### 仿真参数配置

#### 1. 空域设置
空域配置通过 `SettingAirspace.m` 函数完成：

```matlab
% 空域参数
dx = 1500;  % X方向宽度（米）
dy = 1500;  % Y方向长度（米）
dz = 90;    % Z方向高度（米）
asStr = 'NYC';  % 空域类型
UIRun = 0;  % 是否为UI运行模式

[Settings.Airspace] = SettingAirspace(dx, dy, dz, asStr, UIRun);
```

**支持的空域类型**：
- `'NYC'`：纽约场景（高度400米起）
- `'SF'`：旧金山场景（高度40米起）
- `'PAR'`：巴黎场景（高度40米起）
- `'HK'`：香港场景（高度40米起）
- `'LI'`：长岛场景（支持固定航路点）
- `'Subset'`：子集网络场景

#### 2. 飞行器设置
飞行器参数通过 `SettingAircraft.m` 配置：

```matlab
% 飞行器参数
Vmax = [20, 30];  % 速度范围 [最小值, 最大值] (m/s)
Rs = [10, 15];    % 安全半径范围 [最小值, 最大值] (m)

[Settings.Aircraft] = SettingAircraft(Vmax, Rs);
```

**飞行器类型**：
- **EPAV (1)**：电动个人飞行器
- **EUAV (2)**：电动无人机
- **PAV (3)**：个人飞行器（默认类型）
- **UAV (4)**：无人机

#### 3. 仿真设置
仿真时间和流量通过 `SettingSimulation.m` 配置：

```matlab
InflowRate = 10/60;  % 流入率（架/秒）
tfStr = 10;          % 仿真时长设置

[Settings.Sim] = SettingSimulation(InflowRate, tfStr);
```

### 仿真执行流程

1. **初始化阶段**
   - 加载空域配置和垂直机场数据
   - 创建飞行器对象
   - 初始化交通流计算矩阵

2. **仿真循环**
   - 飞行器起飞管理（`AircraftDepartures`）
   - 飞行控制和运动更新（`AircraftController` + `AircraftMotion`）
   - 飞行器到达处理（`AircraftArrivals`）
   - 仿真信息更新（`UpdateSimInfo`）
   - 能耗计算（`CalEC_AG`）
   - 交通流分析（`CalTFC_N`、`CalTFC_Ri`）

3. **结果输出**
   - 生成 JSON 格式的仿真数据
   - 保存 MATLAB 工作空间
   - 生成飞行轨迹动画视频

### 输出文件说明

仿真完成后，系统会在 `Outputs/` 目录下生成以下文件：

```
Outputs/SimOutput_YYYYMMDD_HHMMSS_ScenarioName/
├── Trajectories_ScenarioName.mat    # MATLAB工作空间数据
├── MotionPicture_YYYYMMDD_HHMMSS.avi # 飞行轨迹动画视频
└── ScenarioName_YYYYMMDD_HHMM.json   # JSON格式仿真结果
```

**JSON 输出数据结构**：
```json
{
  "ObjAircraft": [
    {
      "AMI": 3,           // 飞行器类型
      "stat": [...],      // 飞行状态时间序列
      "tda": 15.5,        // 起飞时间（秒）
      "x": [...],         // X坐标时间序列
      "y": [...],         // Y坐标时间序列  
      "z": [...]          // Z坐标时间序列
    }
  ]
}
```

## API 参考

### 主要函数接口

#### RunLAATSim
```matlab
RunLAATSim(InflowRate, SceStr, asStr)
```
**功能**：执行标准 LAAT 仿真

**参数**：
- `InflowRate`：流入率（架/秒）
- `SceStr`：场景名称字符串
- `asStr`：空域类型字符串

**示例**：
```matlab
RunLAATSim(10/60, 'Test1', 'NYC');
```

#### RunLAATSimUI  
```matlab
scenarioName = RunLAATSimUI(InflowRate, NewSettings, SceStr)
```
**功能**：执行带自定义设置的仿真（用于 Web 界面）

**参数**：
- `InflowRate`：流入率（架/秒）
- `NewSettings`：自定义设置结构体
- `SceStr`：场景名称字符串

**返回值**：
- `scenarioName`：生成的场景名称

#### SettingAirspace
```matlab
[Airspace] = SettingAirspace(dx, dy, dz, asStr, UIRun)
```
**功能**：配置空域参数

**参数**：
- `dx, dy, dz`：空域三维尺寸（米）
- `asStr`：空域类型
- `UIRun`：UI运行标志

#### SettingAircraft
```matlab
[Aircraft] = SettingAircraft(Vmax, Rs)
```
**功能**：配置飞行器参数

**参数**：
- `Vmax`：速度范围 [最小值, 最大值]
- `Rs`：安全半径范围 [最小值, 最大值]

#### SettingSimulation
```matlab
[Sim] = SettingSimulation(InflowRate, tfStr)
```
**功能**：配置仿真参数

**参数**：
- `InflowRate`：流入率（架/秒）
- `tfStr`：时长设置参数

### 核心算法模块

#### AircraftController
实现基于人工势场的分布式避碰控制算法：
- 计算飞行器间的相互作用力
- 生成避碰控制指令
- 更新飞行器运动状态

#### CalTFC_N / CalTFC_Ri
交通流特性计算：
- **CalTFC_N**：计算网络级交通流参数（密度、速度、流量）
- **CalTFC_Ri**：计算区域级交通流参数

#### CalEC_AG
能耗计算模型：
- 水平飞行能耗
- 垂直飞行能耗  
- 悬停能耗
- 电池状态管理

## 配置参考

### 空域配置文件

系统支持通过 JSON 文件配置垂直机场和航路点：

**垂直机场配置示例**（`public/Vertiports/FixedVertiportsSettings_V2_NYC.json`）：
```json
[
  {
    "neuDistances": {
      "east": 1000.0,
      "north": 500.0,
      "up": 0.0
    },
    "height": 400
  }
]
```

**航路点配置示例**（`public/Waypoints/FixedWaypointSettings_V3_LI.json`）：
```json
[
  {
    "path_points": [
      {
        "neuDistances": {
          "east": 0.0,
          "north": 0.0, 
          "up": 50.0
        }
      }
    ]
  }
]
```

### 仿真参数调优

#### 性能优化建议

1. **时间步长设置**：
   ```matlab
   % 默认时间步长为0.5秒，可根据需要调整
   Sim.dtsim = 0.5;  % 仿真时间步长（秒）
   ```

2. **飞行器数量控制**：
   ```matlab
   % 通过流入率控制飞行器总数
   InflowRate = 5/60;  % 降低流入率减少计算负担
   ```

3. **空域尺寸优化**：
   ```matlab
   % 较小的空域可以提高计算效率
   dx = 1000; dy = 1000; dz = 60;
   ```

#### 仿真精度设置

1. **避碰算法参数**：
   ```matlab
   Aircraft.Gainfactor_rs = 1.8;  % 安全半径增益因子
   Aircraft.Gainfactor_ra = 1.8;  % 避碰半径增益因子
   ```

2. **能耗模型参数**：
   ```matlab
   Aircraft.Bat_max = 69.5;      % 电池容量（Wh）
   Aircraft.Bat_limit = 13.9;    % 电池安全余量（20%）
   ```

## 项目结构说明

```
pyLAATSimV0200/
├── RunLAATSim.m              # 主仿真入口函数
├── RunLAATSimUI.m            # Web界面仿真入口
├── AircraftController.m      # 飞行器控制算法
├── AircraftMotion.m          # 飞行器运动模型
├── AircraftDepartures.m      # 起飞管理模块
├── AircraftArrivals.m        # 到达管理模块
├── InitAircraftObj.m         # 飞行器对象初始化
├── SettingAirspace.m         # 空域配置模块
├── SettingAircraft.m         # 飞行器配置模块
├── SettingSimulation.m       # 仿真配置模块
├── LoadVertiports.m          # 垂直机场数据加载
├── LoadWaypoints.m           # 航路点数据加载
├── CalTFC_N.m               # 网络交通流计算
├── CalTFC_Ri.m              # 区域交通流计算
├── CalEC_AG.m               # 能耗计算模块
├── UpdateSimInfo.m          # 仿真信息更新
├── ExportJSON.m             # JSON数据导出
├── PlotMotionPicture.m      # 2D轨迹可视化
├── PlotMotionPicture_3DMotion.m # 3D轨迹可视化
├── DataMAT/                 # 仿真数据文件
│   ├── MFD_VTOL_Func_N_Ide6_1Hr.mat
│   ├── MFD_VTOL_Func_Ri1_Ide6_1Hr.mat
│   └── MFD_VTOL_Func_Ri2_Ide6_1Hr.mat
└── Outputs/                 # 仿真结果输出目录
    └── SimOutput_*/         # 具体仿真结果文件夹
```

### 重要文件说明

- **核心仿真引擎**：`RunLAATSim.m` 是标准仿真的主入口
- **Web接口**：`RunLAATSimUI.m` 专为Flask API设计
- **控制算法**：`AircraftController.m` 实现分布式避碰控制
- **交通流分析**：`CalTFC_*.m` 系列函数计算交通流特性
- **可视化**：`PlotMotionPicture*.m` 生成仿真动画
- **配置管理**：`Setting*.m` 系列函数管理各类参数
- **数据处理**：`DataMAT/` 包含预计算的交通流数据

## 常见问题解答

### Q1: 仿真运行时出现内存不足错误
**A**: 这通常是由于飞行器数量过多导致的。解决方案：
1. 降低流入率：`InflowRate = 5/60` 
2. 减少仿真时长：修改 `SettingSimulation.m` 中的时间设置
3. 增加系统内存或使用更强大的计算机

### Q2: 如何添加新的城市场景？
**A**: 需要以下步骤：
1. 在 `LoadVertiports.m` 中添加新的 case 分支
2. 创建对应的垂直机场 JSON 配置文件
3. 如需固定航路，还需创建航路点配置文件
4. 在 `SettingAirspace.m` 中添加相应的配置逻辑

### Q3: 仿真结果如何验证正确性？
**A**: 可以通过以下方式验证：
1. 检查飞行器是否成功避碰（无碰撞事件）
2. 验证能耗计算是否合理（电池电量变化）
3. 观察交通流参数是否符合预期
4. 查看生成的轨迹动画是否正常

### Q4: 如何自定义飞行器类型？
**A**: 修改 `InitAircraftObj.m` 中的以下参数：
```matlab
% 修改飞行器类型分布
ModelsTypes = [0.25, 0.25, 0.25, 0.25];  % EPAV, EUAV, PAV, UAV比例
```

### Q5: 仿真速度太慢怎么办？
**A**: 优化建议：
1. 增大时间步长：`Sim.dtsim = 1.0`
2. 减少可视化频率：修改 `PlotMotionPicture.m` 中的帧率
3. 关闭实时绘图：注释掉绘图相关代码
4. 使用并行计算工具箱加速

### Q6: 如何修改避碰算法参数？
**A**: 在 `AircraftController.m` 中调整以下参数：
```matlab
gamma = 1;      % 势场强度
k2 = 1;         % 控制增益
e = 0.000001;   % 数值稳定性参数
```

### Q7: 输出的JSON文件格式说明
**A**: JSON文件包含每架飞行器的完整轨迹数据：
- `AMI`：飞行器类型标识
- `stat`：状态序列（0-未激活，1-飞行中，2-已到达）
- `tda`：起飞时间
- `x,y,z`：三维坐标时间序列

## 开发指南

### 贡献代码

如果您希望为项目贡献代码，请遵循以下步骤：

1. **Fork 项目**到您的 GitHub 账户
2. **创建功能分支**：`git checkout -b feature/new-feature`
3. **提交更改**：`git commit -am 'Add new feature'`
4. **推送分支**：`git push origin feature/new-feature`
5. **创建 Pull Request**

### 代码规范

- 使用清晰的函数和变量命名
- 为每个函数添加详细的注释说明
- 遵循 MATLAB 编码最佳实践
- 确保代码兼容 MATLAB R2018b 及以上版本

### 测试指南

在提交代码前，请确保：
1. 运行基本仿真测试：`RunLAATSim(5/60, 'Test', 'NYC')`
2. 验证新功能不会破坏现有功能
3. 检查内存使用和性能影响
4. 更新相关文档

## 版本历史

- **v0.2.0** (2024-03-05): 添加Web界面支持，优化交通流计算
- **v0.1.0** (2023-02-08): 初始版本，基本仿真功能

## 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。

## 联系方式

- **项目维护者**：Yazan Safadi, Rao Fu
- **技术支持**：请通过 GitHub Issues 提交问题
- **学术合作**：欢迎联系进行学术交流与合作

## 致谢

感谢所有为本项目做出贡献的研究人员和开发者。本项目的开发得到了相关研究机构的支持。

---

*最后更新：2024年3月*
