classdef RealTimeSimulationEngine < handle
    % RealTimeSimulationEngine - 真正的实时LAAT仿真引擎
    %
    % 这是一个完全重新设计的实时仿真系统，使用事件驱动架构
    % 和异步处理机制，提供真正的实时仿真体验。
    %
    % 主要特性:
    %   - 基于timer的真实时间驱动
    %   - 状态机管理仿真状态
    %   - 异步计算和渲染
    %   - 交互式控制界面
    %   - 可暂停/继续/重置
    %
    % 使用示例:
    %   engine = RealTimeSimulationEngine();
    %   engine.initialize(InflowRate, SceStr, asStr);
    %   engine.start();
    %
    % 作者: AI Assistant
    % 创建日期: 2024-09-26
    
    properties (Access = private)
        % 仿真状态
        SimState = 'stopped'  % 'stopped', 'running', 'paused', 'finished'
        
        % 时间管理
        SimulationTimer       % 主仿真定时器
        RenderTimer          % 渲染定时器
        SimTime = 0          % 当前仿真时间
        RealStartTime        % 实际开始时间
        TimeScale = 1        % 时间缩放因子 (仿真时间/实际时间)
        
        % 仿真参数
        Settings             % 仿真设置
        SimInfo              % 仿真信息
        ObjAircraft          % 飞行器对象数组
        TFC                  % 交通流数据
        
        % 实时数据缓冲区
        DataBuffer           % 循环缓冲区存储实时数据
        BufferSize = 1000    % 缓冲区大小
        CurrentIndex = 1     % 当前数据索引
        
        % 可视化组件
        MainFigure           % 主窗口
        ControlPanel         % 控制面板
        Axes3D               % 3D显示区域
        InfoPanels           % 信息显示面板
        
        % 控制组件
        PlayPauseButton      % 播放/暂停按钮
        ResetButton          % 重置按钮
        SpeedSlider          % 速度控制滑块
        TimeSlider           % 时间跳转滑块
        
        % 性能监控
        FrameRate = 30       % 目标帧率
        SimStepRate = 20     % 仿真步进频率 (Hz)
        LastFrameTime        % 上一帧时间
        FrameCount = 0       % 帧计数器
        
        % 仿真参数
        InflowRate
        ScenarioName
        AirspaceType
    end
    
    methods (Access = public)
        function obj = RealTimeSimulationEngine()
            % 构造函数
            obj.initializeTimers();
            obj.initializeDataBuffer();
        end
        
        function initialize(obj, inflowRate, scenarioName, airspaceType, varargin)
            % 初始化仿真参数和环境
            
            % 解析输入参数
            p = inputParser;
            addParameter(p, 'TimeScale', 1, @isnumeric);
            addParameter(p, 'FrameRate', 30, @isnumeric);
            addParameter(p, 'SimStepRate', 20, @isnumeric);
            parse(p, varargin{:});
            
            obj.InflowRate = inflowRate;
            obj.ScenarioName = scenarioName;
            obj.AirspaceType = airspaceType;
            obj.TimeScale = p.Results.TimeScale;
            obj.FrameRate = p.Results.FrameRate;
            obj.SimStepRate = p.Results.SimStepRate;
            
            % 初始化仿真设置
            obj.initializeSimulationSettings();
            
            % 创建用户界面
            obj.createUserInterface();
            
            % 初始化仿真对象
            obj.initializeSimulationObjects();
            
            % 重置仿真状态
            obj.reset();
            
            fprintf('实时仿真引擎初始化完成\n');
            fprintf('场景: %s, 空域: %s, 流入率: %.3f 架/秒\n', ...
                    scenarioName, airspaceType, inflowRate);
            fprintf('时间缩放: %.1fx, 帧率: %d fps, 仿真频率: %d Hz\n', ...
                    obj.TimeScale, obj.FrameRate, obj.SimStepRate);
        end
        
        function start(obj)
            % 开始仿真
            if strcmp(obj.SimState, 'finished')
                obj.reset();
            end
            
            obj.SimState = 'running';
            obj.RealStartTime = tic;
            
            % 启动定时器
            start(obj.SimulationTimer);
            start(obj.RenderTimer);
            
            % 更新UI
            obj.updateControlButtons();
            
            fprintf('实时仿真开始运行...\n');
        end
        
        function pause(obj)
            % 暂停仿真
            if strcmp(obj.SimState, 'running')
                obj.SimState = 'paused';
                stop(obj.SimulationTimer);
                stop(obj.RenderTimer);
                obj.updateControlButtons();
                fprintf('仿真已暂停\n');
            end
        end
        
        function resume(obj)
            % 继续仿真
            if strcmp(obj.SimState, 'paused')
                obj.SimState = 'running';
                obj.RealStartTime = tic - obj.SimTime / obj.TimeScale;
                start(obj.SimulationTimer);
                start(obj.RenderTimer);
                obj.updateControlButtons();
                fprintf('仿真已继续\n');
            end
        end
        
        function stop(obj)
            % 停止仿真
            obj.SimState = 'stopped';
            stop(obj.SimulationTimer);
            stop(obj.RenderTimer);
            obj.updateControlButtons();
            fprintf('仿真已停止\n');
        end
        
        function reset(obj)
            % 重置仿真
            obj.stop();
            obj.SimTime = 0;
            obj.CurrentIndex = 1;
            obj.FrameCount = 0;
            
            % 重置仿真对象
            if ~isempty(obj.SimInfo)
                obj.SimInfo.t = 0;
                obj.SimInfo.Mact = [];
                obj.SimInfo.Marr = [];
                obj.SimInfo.Mque = [];
                obj.SimInfo.MactBQ = [];
            end
            
            % 清空数据缓冲区
            obj.initializeDataBuffer();
            
            % 更新显示
            obj.updateVisualization();
            obj.updateInfoPanels();
            
            fprintf('仿真已重置\n');
        end
        
        function delete(obj)
            % 析构函数
            obj.cleanup();
        end
    end
    
    methods (Access = private)
        function initializeTimers(obj)
            % 初始化定时器
            
            % 仿真计算定时器
            obj.SimulationTimer = timer(...
                'ExecutionMode', 'fixedRate', ...
                'Period', 1/obj.SimStepRate, ...
                'TimerFcn', @(~,~) obj.simulationStep(), ...
                'ErrorFcn', @(~,~) obj.handleTimerError('Simulation'));
            
            % 渲染定时器
            obj.RenderTimer = timer(...
                'ExecutionMode', 'fixedRate', ...
                'Period', 1/obj.FrameRate, ...
                'TimerFcn', @(~,~) obj.renderFrame(), ...
                'ErrorFcn', @(~,~) obj.handleTimerError('Render'));
        end
        
        function initializeDataBuffer(obj)
            % 初始化数据缓冲区
            obj.DataBuffer = struct();
            obj.DataBuffer.Time = zeros(obj.BufferSize, 1);
            obj.DataBuffer.ActiveAircraft = cell(obj.BufferSize, 1);
            obj.DataBuffer.Positions = cell(obj.BufferSize, 1);
            obj.DataBuffer.Statistics = cell(obj.BufferSize, 1);
            obj.CurrentIndex = 1;
        end
        
        function initializeSimulationSettings(obj)
            % 初始化仿真设置（复用原有设置函数）
            UIRun = 0;
            obj.Settings = struct();
            obj.Settings.Airspace = SettingAirspace(1500, 1500, 90, obj.AirspaceType, UIRun);
            obj.Settings.Aircraft = SettingAircraft([20,20], [10,10]);
            obj.Settings.Sim = SettingSimulation(obj.InflowRate, 10);
        end
        
        function initializeSimulationObjects(obj)
            % 初始化仿真对象
            obj.SimInfo = struct();
            obj.SimInfo.Mina = [];
            obj.SimInfo.Mque = [];
            obj.SimInfo.Mact = [];
            obj.SimInfo.Marr = [];
            obj.SimInfo.MactBQ = [];
            obj.SimInfo.M = 1:1:obj.Settings.Sim.M;
            obj.SimInfo.cc = 0;
            obj.SimInfo.dtS = obj.Settings.Sim.dtsim;
            obj.SimInfo.dtM = obj.Settings.Sim.dtMFD;
            obj.SimInfo.tf = obj.Settings.Sim.tf;
            obj.SimInfo.t = 0;
            
            % 初始化数据矩阵（使用循环缓冲区而不是预分配整个数组）
            obj.SimInfo.pdt = zeros(obj.BufferSize, 3*length(obj.SimInfo.M));
            obj.SimInfo.vdt = zeros(obj.BufferSize, 3*length(obj.SimInfo.M));
            obj.SimInfo.statusdt = zeros(obj.BufferSize, length(obj.SimInfo.M));
            obj.SimInfo.ridt = zeros(obj.BufferSize, length(obj.SimInfo.M));
            
            % 初始化飞行器对象
            [obj.SimInfo, obj.ObjAircraft] = InitAircraftObj(obj.SimInfo, obj.Settings);
            
            % 初始化交通流数据
            obj.TFC = struct();
            obj.TFC.CS = [];
            obj.TFC.EC = [];
            obj.TFC.EC.ECdt = zeros(obj.BufferSize, length(obj.SimInfo.M));
            obj.TFC.EC.sumECtdt = zeros(obj.BufferSize, 1);
            obj.TFC.EC.sumECqdt = zeros(obj.BufferSize, 1);
            obj.TFC.EC.avgECtdt = zeros(obj.BufferSize, 1);
            obj.TFC.EC.avgECqdt = zeros(obj.BufferSize, 1);
            obj.TFC.EC.sumECdt = zeros(obj.BufferSize, 1);
        end
        
        function simulationStep(obj)
            % 执行一个仿真步骤（在定时器回调中执行）
            try
                if ~strcmp(obj.SimState, 'running')
                    return;
                end
                
                % 更新仿真时间
                realElapsed = toc(obj.RealStartTime);
                obj.SimTime = realElapsed * obj.TimeScale;
                
                % 检查是否超过仿真时长
                if obj.SimTime >= obj.Settings.Sim.tf
                    obj.SimState = 'finished';
                    obj.stop();
                    fprintf('仿真完成！总时长: %.1f 秒\n', obj.SimTime);
                    return;
                end
                
                % 更新SimInfo时间
                obj.SimInfo.t = obj.SimTime;
                
                % 执行仿真步骤（复用原有仿真逻辑）
                [obj.SimInfo, obj.ObjAircraft] = AircraftDepartures(obj.SimInfo, obj.ObjAircraft);
                [obj.SimInfo, obj.ObjAircraft] = AircraftController(obj.SimInfo, obj.ObjAircraft, obj.Settings);
                [obj.SimInfo, obj.ObjAircraft] = AircraftArrivals(obj.SimInfo, obj.ObjAircraft);
                
                % 更新仿真信息到缓冲区
                obj.updateSimInfoBuffer();
                
                % 计算能耗
                if obj.SimTime > 0
                    [obj.TFC.EC, obj.ObjAircraft] = CalEC_AG(obj.TFC.EC, obj.SimInfo, obj.ObjAircraft);
                end
                
                % 计算交通流（降低频率以提高性能）
                if obj.SimTime > 0 && mod(obj.SimTime, obj.SimInfo.dtM) < obj.SimInfo.dtS
                    try
                        [obj.TFC] = CalTFC_N(obj.TFC, obj.SimInfo, obj.ObjAircraft, obj.Settings);
                        [obj.TFC] = CalTFC_Ri(obj.TFC, obj.SimInfo, obj.ObjAircraft, obj.Settings);
                    catch
                        % 交通流计算失败时继续仿真
                    end
                end
                
                % 存储当前状态到数据缓冲区
                obj.storeCurrentState();
                
            catch ME
                fprintf('仿真步骤执行错误: %s\n', ME.message);
                obj.pause();
            end
        end
        
        function updateSimInfoBuffer(obj)
            % 更新SimInfo缓冲区（循环缓冲区模式）
            idx = mod(obj.CurrentIndex - 1, obj.BufferSize) + 1;
            
            if ~isempty(obj.ObjAircraft)
                positions = cat(1, [obj.ObjAircraft.pt]);
                velocities = cat(1, [obj.ObjAircraft.vt]);
                statuses = cat(1, [obj.ObjAircraft.status]);
                regions = cat(1, [obj.ObjAircraft.rit]);
                
                if ~isempty(positions)
                    obj.SimInfo.pdt(idx, 1:length(positions)) = positions(:)';
                end
                if ~isempty(velocities)
                    obj.SimInfo.vdt(idx, 1:length(velocities)) = velocities(:)';
                end
                if ~isempty(statuses)
                    obj.SimInfo.statusdt(idx, 1:length(statuses)) = statuses(:)';
                end
                if ~isempty(regions)
                    obj.SimInfo.ridt(idx, 1:length(regions)) = regions(:)';
                end
            end
        end
        
        function storeCurrentState(obj)
            % 存储当前状态到数据缓冲区
            idx = mod(obj.CurrentIndex - 1, obj.BufferSize) + 1;
            
            obj.DataBuffer.Time(idx) = obj.SimTime;
            obj.DataBuffer.ActiveAircraft{idx} = obj.SimInfo.Mact;
            
            % 存储位置信息
            if ~isempty(obj.SimInfo.Mact)
                positions = zeros(length(obj.SimInfo.Mact), 3);
                for i = 1:length(obj.SimInfo.Mact)
                    aa = obj.SimInfo.Mact(i);
                    if aa <= length(obj.ObjAircraft)
                        positions(i, :) = obj.ObjAircraft(aa).pt;
                    end
                end
                obj.DataBuffer.Positions{idx} = positions;
            else
                obj.DataBuffer.Positions{idx} = [];
            end
            
            % 存储统计信息
            stats = struct();
            stats.ActiveCount = length(obj.SimInfo.Mact);
            stats.ArrivedCount = length(obj.SimInfo.Marr);
            stats.QueuedCount = length(obj.SimInfo.Mque);
            obj.DataBuffer.Statistics{idx} = stats;
            
            obj.CurrentIndex = obj.CurrentIndex + 1;
        end
        
        function renderFrame(obj)
            % 渲染一帧（在渲染定时器回调中执行）
            try
                if isempty(obj.MainFigure) || ~isvalid(obj.MainFigure)
                    return;
                end
                
                % 更新可视化
                obj.updateVisualization();
                
                % 更新信息面板
                obj.updateInfoPanels();
                
                % 更新控制界面
                obj.updateControlInterface();
                
                % 强制刷新显示
                drawnow limitrate;
                
                obj.FrameCount = obj.FrameCount + 1;
                
            catch ME
                fprintf('渲染错误: %s\n', ME.message);
            end
        end
        
        function handleTimerError(obj, timerType)
            % 处理定时器错误
            fprintf('定时器错误 (%s)，暂停仿真\n', timerType);
            obj.pause();
        end
        
        function createUserInterface(obj)
            % 创建用户界面

            % 创建主窗口
            obj.MainFigure = figure('Name', '实时LAAT仿真引擎', ...
                                   'NumberTitle', 'off', ...
                                   'Position', [100, 100, 1400, 900], ...
                                   'Color', [0.94, 0.94, 0.94], ...
                                   'CloseRequestFcn', @(~,~) obj.closeCallback(), ...
                                   'KeyPressFcn', @(~,evt) obj.keyPressCallback(evt));

            % 创建控制面板
            obj.createControlPanel();

            % 创建3D显示区域
            obj.create3DDisplay();

            % 创建信息面板
            obj.createInfoPanels();
        end

        function createControlPanel(obj)
            % 创建控制面板

            % 控制面板背景
            obj.ControlPanel = uipanel('Parent', obj.MainFigure, ...
                                      'Title', '仿真控制', ...
                                      'FontSize', 12, 'FontWeight', 'bold', ...
                                      'Position', [0.02, 0.85, 0.96, 0.13], ...
                                      'BackgroundColor', [0.9, 0.9, 0.9]);

            % 播放/暂停按钮
            obj.PlayPauseButton = uicontrol('Parent', obj.ControlPanel, ...
                                           'Style', 'pushbutton', ...
                                           'String', '▶ 开始', ...
                                           'FontSize', 14, 'FontWeight', 'bold', ...
                                           'Position', [20, 40, 100, 40], ...
                                           'BackgroundColor', [0.2, 0.8, 0.2], ...
                                           'Callback', @(~,~) obj.togglePlayPause());

            % 重置按钮
            obj.ResetButton = uicontrol('Parent', obj.ControlPanel, ...
                                       'Style', 'pushbutton', ...
                                       'String', '⟲ 重置', ...
                                       'FontSize', 12, 'FontWeight', 'bold', ...
                                       'Position', [140, 40, 80, 40], ...
                                       'BackgroundColor', [0.8, 0.8, 0.2], ...
                                       'Callback', @(~,~) obj.reset());

            % 速度控制
            uicontrol('Parent', obj.ControlPanel, 'Style', 'text', ...
                     'String', '仿真速度:', 'FontSize', 10, ...
                     'Position', [250, 65, 80, 20], ...
                     'BackgroundColor', [0.9, 0.9, 0.9]);

            obj.SpeedSlider = uicontrol('Parent', obj.ControlPanel, ...
                                       'Style', 'slider', ...
                                       'Min', 0.1, 'Max', 5, 'Value', obj.TimeScale, ...
                                       'Position', [250, 40, 150, 20], ...
                                       'Callback', @(src,~) obj.updateTimeScale(src.Value));

            % 速度显示
            uicontrol('Parent', obj.ControlPanel, 'Style', 'text', ...
                     'String', sprintf('%.1fx', obj.TimeScale), ...
                     'FontSize', 10, 'Tag', 'SpeedDisplay', ...
                     'Position', [410, 40, 40, 20], ...
                     'BackgroundColor', [0.9, 0.9, 0.9]);

            % 时间跳转
            uicontrol('Parent', obj.ControlPanel, 'Style', 'text', ...
                     'String', '时间跳转:', 'FontSize', 10, ...
                     'Position', [480, 65, 80, 20], ...
                     'BackgroundColor', [0.9, 0.9, 0.9]);

            obj.TimeSlider = uicontrol('Parent', obj.ControlPanel, ...
                                      'Style', 'slider', ...
                                      'Min', 0, 'Max', obj.Settings.Sim.tf, 'Value', 0, ...
                                      'Position', [480, 40, 200, 20], ...
                                      'Callback', @(src,~) obj.jumpToTime(src.Value));

            % 时间显示
            uicontrol('Parent', obj.ControlPanel, 'Style', 'text', ...
                     'String', '00:00 / 10:00', ...
                     'FontSize', 10, 'Tag', 'TimeDisplay', ...
                     'Position', [690, 40, 100, 20], ...
                     'BackgroundColor', [0.9, 0.9, 0.9]);

            % 统计信息快速显示
            uicontrol('Parent', obj.ControlPanel, 'Style', 'text', ...
                     'String', '活跃: 0 | 到达: 0 | 排队: 0', ...
                     'FontSize', 10, 'Tag', 'QuickStats', ...
                     'Position', [820, 50, 200, 20], ...
                     'BackgroundColor', [0.9, 0.9, 0.9]);

            % 帧率显示
            uicontrol('Parent', obj.ControlPanel, 'Style', 'text', ...
                     'String', 'FPS: 0', ...
                     'FontSize', 10, 'Tag', 'FPSDisplay', ...
                     'Position', [1050, 50, 60, 20], ...
                     'BackgroundColor', [0.9, 0.9, 0.9]);
        end

        function create3DDisplay(obj)
            % 创建3D显示区域
            obj.Axes3D = axes('Parent', obj.MainFigure, ...
                             'Position', [0.02, 0.15, 0.65, 0.68]);

            hold(obj.Axes3D, 'on');
            grid(obj.Axes3D, 'on');
            axis(obj.Axes3D, 'equal');

            % 设置坐标轴
            AirspaceS = obj.Settings.Airspace;
            AxisSize = 1.2 * max([AirspaceS.dx, AirspaceS.dy, AirspaceS.dz]);
            axis(obj.Axes3D, [-AxisSize/2, AxisSize/2, -AxisSize/2, AxisSize/2, 0, AxisSize]);

            xlabel(obj.Axes3D, 'X [m]', 'FontSize', 12);
            ylabel(obj.Axes3D, 'Y [m]', 'FontSize', 12);
            zlabel(obj.Axes3D, 'Z [m]', 'FontSize', 12);
            title(obj.Axes3D, '实时飞行器轨迹显示', 'FontSize', 14, 'FontWeight', 'bold');

            view(obj.Axes3D, [45, 30]);

            % 绘制空域边界
            obj.drawAirspace();
        end

        function createInfoPanels(obj)
            % 创建信息显示面板

            % 仿真状态面板
            uipanel('Parent', obj.MainFigure, ...
                   'Title', '仿真状态', 'FontSize', 11, 'FontWeight', 'bold', ...
                   'Position', [0.69, 0.75, 0.29, 0.08], ...
                   'BackgroundColor', [0.95, 0.95, 0.95]);

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '状态: 已停止', 'FontSize', 10, ...
                     'Position', [0.71, 0.80, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'StatusText');

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '时间: 0.0 / 600.0 秒', 'FontSize', 10, ...
                     'Position', [0.71, 0.77, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'TimeText');

            % 飞行器统计面板
            uipanel('Parent', obj.MainFigure, ...
                   'Title', '飞行器统计', 'FontSize', 11, 'FontWeight', 'bold', ...
                   'Position', [0.69, 0.65, 0.29, 0.08], ...
                   'BackgroundColor', [0.95, 0.95, 0.95]);

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '活跃飞行器: 0', 'FontSize', 10, ...
                     'Position', [0.71, 0.70, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'ActiveText');

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '已到达: 0 | 排队: 0', 'FontSize', 10, ...
                     'Position', [0.71, 0.67, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'CountText');

            % 交通流信息面板
            uipanel('Parent', obj.MainFigure, ...
                   'Title', '交通流信息', 'FontSize', 11, 'FontWeight', 'bold', ...
                   'Position', [0.69, 0.55, 0.29, 0.08], ...
                   'BackgroundColor', [0.95, 0.95, 0.95]);

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '密度: N/A', 'FontSize', 10, ...
                     'Position', [0.71, 0.60, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'DensityText');

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '平均速度: N/A', 'FontSize', 10, ...
                     'Position', [0.71, 0.57, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'SpeedText');

            % 能耗信息面板
            uipanel('Parent', obj.MainFigure, ...
                   'Title', '能耗统计', 'FontSize', 11, 'FontWeight', 'bold', ...
                   'Position', [0.69, 0.45, 0.29, 0.08], ...
                   'BackgroundColor', [0.95, 0.95, 0.95]);

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '总能耗: 0.0 Wh', 'FontSize', 10, ...
                     'Position', [0.71, 0.50, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'EnergyText');

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '平均能耗: 0.0 Wh', 'FontSize', 10, ...
                     'Position', [0.71, 0.47, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'AvgEnergyText');

            % 性能监控面板
            uipanel('Parent', obj.MainFigure, ...
                   'Title', '性能监控', 'FontSize', 11, 'FontWeight', 'bold', ...
                   'Position', [0.69, 0.35, 0.29, 0.08], ...
                   'BackgroundColor', [0.95, 0.95, 0.95]);

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '渲染FPS: 0', 'FontSize', 10, ...
                     'Position', [0.71, 0.40, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'RenderFPSText');

            uicontrol('Parent', obj.MainFigure, 'Style', 'text', ...
                     'String', '仿真频率: 0 Hz', 'FontSize', 10, ...
                     'Position', [0.71, 0.37, 0.25, 0.02], ...
                     'BackgroundColor', [0.95, 0.95, 0.95], ...
                     'Tag', 'SimFreqText');

            % 实时图表区域（预留）
            uipanel('Parent', obj.MainFigure, ...
                   'Title', '实时数据图表', 'FontSize', 11, 'FontWeight', 'bold', ...
                   'Position', [0.69, 0.15, 0.29, 0.18], ...
                   'BackgroundColor', [0.95, 0.95, 0.95]);
        end

        function drawAirspace(obj)
            % 绘制空域边界
            AirspaceS = obj.Settings.Airspace;

            % 主空域边界
            obj.drawCube(AirspaceS.xyz, 'none', 0.1, 'black', 2, '-');

            % VTOL区域边界
            if AirspaceS.VTOL
                obj.drawCube(AirspaceS.VTOLxyz, 'none', 0.05, 'blue', 1, '--');
            end

            % 简化的区域网格
            if isfield(AirspaceS, 'Regions') && AirspaceS.Regions.n > 0
                for ri = 1:min(AirspaceS.Regions.n, 10) % 限制数量
                    if mod(ri, 2) == 1
                        obj.drawCube(AirspaceS.Regions.B(ri).xyz, 'none', 0.02, 'gray', 0.5, ':');
                    end
                end
            end
        end

        function drawCube(obj, xyz, faceColor, faceAlpha, edgeColor, lineWidth, lineStyle)
            % 绘制立方体
            point1 = xyz(1,:);
            point2 = xyz(2,:);
            center = (point1 + point2) / 2;
            dxyz = abs(point2 - point1);

            % 定义立方体的8个顶点
            vertices = [
                center + [ dxyz(1)/2,  dxyz(2)/2,  dxyz(3)/2];
                center + [-dxyz(1)/2,  dxyz(2)/2,  dxyz(3)/2];
                center + [-dxyz(1)/2, -dxyz(2)/2,  dxyz(3)/2];
                center + [ dxyz(1)/2, -dxyz(2)/2,  dxyz(3)/2];
                center + [ dxyz(1)/2,  dxyz(2)/2, -dxyz(3)/2];
                center + [-dxyz(1)/2,  dxyz(2)/2, -dxyz(3)/2];
                center + [-dxyz(1)/2, -dxyz(2)/2, -dxyz(3)/2];
                center + [ dxyz(1)/2, -dxyz(2)/2, -dxyz(3)/2];
            ];

            % 定义立方体的6个面
            faces = [1 2 3 4; 2 6 7 3; 4 3 7 8; 1 5 8 4; 1 2 6 5; 5 6 7 8];

            % 绘制立方体
            patch('Parent', obj.Axes3D, 'Vertices', vertices, 'Faces', faces, ...
                  'FaceColor', faceColor, 'FaceAlpha', faceAlpha, ...
                  'EdgeColor', edgeColor, 'LineWidth', lineWidth, 'LineStyle', lineStyle);
        end

        function updateVisualization(obj)
            % 更新3D可视化显示
            if isempty(obj.Axes3D) || ~isvalid(obj.Axes3D)
                return;
            end

            % 清除之前的飞行器图形
            children = get(obj.Axes3D, 'Children');
            for i = length(children):-1:1
                if isprop(children(i), 'Tag') && strcmp(get(children(i), 'Tag'), 'aircraft')
                    delete(children(i));
                end
            end

            % 获取当前数据
            idx = mod(obj.CurrentIndex - 2, obj.BufferSize) + 1; % 上一帧的数据
            if idx < 1 || isempty(obj.DataBuffer.ActiveAircraft{idx})
                return;
            end

            activeAircraft = obj.DataBuffer.ActiveAircraft{idx};
            positions = obj.DataBuffer.Positions{idx};

            if isempty(activeAircraft) || isempty(positions)
                return;
            end

            % 定义飞行器类型颜色
            aircraft_colors = [
                1.0, 0.0, 0.0;  % EPAV - 红色
                0.0, 1.0, 0.0;  % EUAV - 绿色
                0.0, 0.0, 1.0;  % PAV - 蓝色
                1.0, 0.5, 0.0;  % UAV - 橙色
            ];

            % 绘制每架飞行器
            for i = 1:length(activeAircraft)
                aa = activeAircraft(i);
                if aa > length(obj.ObjAircraft) || i > size(positions, 1)
                    continue;
                end

                pos = positions(i, :);
                if all(pos == 0)
                    continue;
                end

                % 获取飞行器类型和颜色
                aircraft_type = obj.ObjAircraft(aa).AMI;
                color = aircraft_colors(min(aircraft_type, 4), :);

                % 绘制飞行器当前位置
                plot3(obj.Axes3D, pos(1), pos(2), pos(3), 'o', ...
                      'Color', color, 'MarkerSize', 8, 'MarkerFaceColor', color, ...
                      'Tag', 'aircraft');

                % 绘制历史轨迹（最近几个点）
                obj.drawTrajectory(aa, color);

                % 绘制目标点和连线
                if isfield(obj.ObjAircraft(aa), 'd') && ~isempty(obj.ObjAircraft(aa).d)
                    target = obj.ObjAircraft(aa).d;
                    plot3(obj.Axes3D, target(1), target(2), target(3), ...
                          'x', 'Color', color, 'MarkerSize', 6, 'LineWidth', 2, 'Tag', 'aircraft');

                    plot3(obj.Axes3D, [pos(1), target(1)], [pos(2), target(2)], [pos(3), target(3)], ...
                          '--', 'Color', [color, 0.3], 'LineWidth', 1, 'Tag', 'aircraft');
                end
            end
        end

        function drawTrajectory(obj, aircraftId, color)
            % 绘制飞行器轨迹
            trajectory_length = min(50, obj.CurrentIndex - 1); % 最近50个点
            if trajectory_length < 2
                return;
            end

            x_traj = zeros(trajectory_length, 1);
            y_traj = zeros(trajectory_length, 1);
            z_traj = zeros(trajectory_length, 1);

            valid_count = 0;
            for i = 1:trajectory_length
                idx = mod(obj.CurrentIndex - i - 1, obj.BufferSize) + 1;
                if idx < 1 || isempty(obj.DataBuffer.ActiveAircraft{idx})
                    continue;
                end

                activeList = obj.DataBuffer.ActiveAircraft{idx};
                positions = obj.DataBuffer.Positions{idx};

                aircraft_idx = find(activeList == aircraftId, 1);
                if ~isempty(aircraft_idx) && aircraft_idx <= size(positions, 1)
                    pos = positions(aircraft_idx, :);
                    if ~all(pos == 0)
                        valid_count = valid_count + 1;
                        x_traj(valid_count) = pos(1);
                        y_traj(valid_count) = pos(2);
                        z_traj(valid_count) = pos(3);
                    end
                end
            end

            if valid_count > 1
                plot3(obj.Axes3D, x_traj(1:valid_count), y_traj(1:valid_count), z_traj(1:valid_count), ...
                      '-', 'Color', [color, 0.6], 'LineWidth', 1.5, 'Tag', 'aircraft');
            end
        end

        function updateInfoPanels(obj)
            % 更新信息显示面板
            if isempty(obj.MainFigure) || ~isvalid(obj.MainFigure)
                return;
            end

            % 更新状态文本
            statusText = findobj(obj.MainFigure, 'Tag', 'StatusText');
            if ~isempty(statusText)
                statusStr = sprintf('状态: %s', obj.getStateString());
                set(statusText, 'String', statusStr);
            end

            % 更新时间文本
            timeText = findobj(obj.MainFigure, 'Tag', 'TimeText');
            if ~isempty(timeText)
                timeStr = sprintf('时间: %.1f / %.1f 秒', obj.SimTime, obj.Settings.Sim.tf);
                set(timeText, 'String', timeStr);
            end

            % 更新飞行器统计
            activeText = findobj(obj.MainFigure, 'Tag', 'ActiveText');
            countText = findobj(obj.MainFigure, 'Tag', 'CountText');
            if ~isempty(activeText) && ~isempty(countText)
                activeCount = length(obj.SimInfo.Mact);
                arrivedCount = length(obj.SimInfo.Marr);
                queuedCount = length(obj.SimInfo.Mque);

                set(activeText, 'String', sprintf('活跃飞行器: %d', activeCount));
                set(countText, 'String', sprintf('已到达: %d | 排队: %d', arrivedCount, queuedCount));
            end

            % 更新交通流信息
            densityText = findobj(obj.MainFigure, 'Tag', 'DensityText');
            speedText = findobj(obj.MainFigure, 'Tag', 'SpeedText');
            if ~isempty(densityText) && ~isempty(speedText)
                if ~isempty(obj.TFC) && isfield(obj.TFC, 'N') && ~isempty(obj.TFC.N)
                    if isfield(obj.TFC.N, 'n') && ~isempty(obj.TFC.N.n)
                        density = obj.TFC.N.n(end);
                        set(densityText, 'String', sprintf('密度: %.2f', density));
                    end
                    set(speedText, 'String', sprintf('平均速度: %.1f m/s', 20)); % 简化
                else
                    set(densityText, 'String', '密度: 计算中...');
                    set(speedText, 'String', '平均速度: N/A');
                end
            end

            % 更新能耗信息
            energyText = findobj(obj.MainFigure, 'Tag', 'EnergyText');
            avgEnergyText = findobj(obj.MainFigure, 'Tag', 'AvgEnergyText');
            if ~isempty(energyText) && ~isempty(avgEnergyText)
                if ~isempty(obj.TFC) && isfield(obj.TFC, 'EC') && ~isempty(obj.TFC.EC)
                    if isfield(obj.TFC.EC, 'sumECdt') && ~isempty(obj.TFC.EC.sumECdt)
                        totalEnergy = obj.TFC.EC.sumECdt(end);
                        avgEnergy = totalEnergy / max(1, length(obj.SimInfo.Mact));
                        set(energyText, 'String', sprintf('总能耗: %.2f Wh', totalEnergy));
                        set(avgEnergyText, 'String', sprintf('平均能耗: %.2f Wh', avgEnergy));
                    end
                else
                    set(energyText, 'String', '总能耗: 0.0 Wh');
                    set(avgEnergyText, 'String', '平均能耗: 0.0 Wh');
                end
            end

            % 更新性能监控
            renderFPSText = findobj(obj.MainFigure, 'Tag', 'RenderFPSText');
            simFreqText = findobj(obj.MainFigure, 'Tag', 'SimFreqText');
            if ~isempty(renderFPSText) && ~isempty(simFreqText)
                currentFPS = obj.calculateCurrentFPS();
                set(renderFPSText, 'String', sprintf('渲染FPS: %.1f', currentFPS));
                set(simFreqText, 'String', sprintf('仿真频率: %d Hz', obj.SimStepRate));
            end
        end

        function updateControlInterface(obj)
            % 更新控制界面
            if isempty(obj.MainFigure) || ~isvalid(obj.MainFigure)
                return;
            end

            % 更新时间滑块
            if ~isempty(obj.TimeSlider) && isvalid(obj.TimeSlider)
                set(obj.TimeSlider, 'Value', obj.SimTime);
            end

            % 更新时间显示
            timeDisplay = findobj(obj.MainFigure, 'Tag', 'TimeDisplay');
            if ~isempty(timeDisplay)
                currentMin = floor(obj.SimTime / 60);
                currentSec = mod(obj.SimTime, 60);
                totalMin = floor(obj.Settings.Sim.tf / 60);
                totalSec = mod(obj.Settings.Sim.tf, 60);
                timeStr = sprintf('%02d:%02d / %02d:%02d', currentMin, currentSec, totalMin, totalSec);
                set(timeDisplay, 'String', timeStr);
            end

            % 更新快速统计
            quickStats = findobj(obj.MainFigure, 'Tag', 'QuickStats');
            if ~isempty(quickStats)
                activeCount = length(obj.SimInfo.Mact);
                arrivedCount = length(obj.SimInfo.Marr);
                queuedCount = length(obj.SimInfo.Mque);
                statsStr = sprintf('活跃: %d | 到达: %d | 排队: %d', activeCount, arrivedCount, queuedCount);
                set(quickStats, 'String', statsStr);
            end

            % 更新FPS显示
            fpsDisplay = findobj(obj.MainFigure, 'Tag', 'FPSDisplay');
            if ~isempty(fpsDisplay)
                currentFPS = obj.calculateCurrentFPS();
                set(fpsDisplay, 'String', sprintf('FPS: %.0f', currentFPS));
            end

            % 更新速度显示
            speedDisplay = findobj(obj.MainFigure, 'Tag', 'SpeedDisplay');
            if ~isempty(speedDisplay)
                set(speedDisplay, 'String', sprintf('%.1fx', obj.TimeScale));
            end
        end

        function togglePlayPause(obj)
            % 切换播放/暂停状态
            switch obj.SimState
                case 'stopped'
                    obj.start();
                case 'running'
                    obj.pause();
                case 'paused'
                    obj.resume();
                case 'finished'
                    obj.reset();
                    obj.start();
            end
        end

        function updateControlButtons(obj)
            % 更新控制按钮状态
            if isempty(obj.PlayPauseButton) || ~isvalid(obj.PlayPauseButton)
                return;
            end

            switch obj.SimState
                case 'stopped'
                    set(obj.PlayPauseButton, 'String', '▶ 开始', 'BackgroundColor', [0.2, 0.8, 0.2]);
                case 'running'
                    set(obj.PlayPauseButton, 'String', '⏸ 暂停', 'BackgroundColor', [0.8, 0.8, 0.2]);
                case 'paused'
                    set(obj.PlayPauseButton, 'String', '▶ 继续', 'BackgroundColor', [0.2, 0.8, 0.2]);
                case 'finished'
                    set(obj.PlayPauseButton, 'String', '⟲ 重新开始', 'BackgroundColor', [0.2, 0.2, 0.8]);
            end
        end

        function updateTimeScale(obj, newScale)
            % 更新时间缩放因子
            obj.TimeScale = newScale;

            % 如果仿真正在运行，需要调整定时器
            if strcmp(obj.SimState, 'running')
                % 重新计算实际开始时间以保持时间连续性
                realElapsed = toc(obj.RealStartTime);
                obj.RealStartTime = tic - obj.SimTime / obj.TimeScale;
            end

            fprintf('时间缩放更新为: %.1fx\n', newScale);
        end

        function jumpToTime(obj, targetTime)
            % 跳转到指定时间（简化实现）
            if strcmp(obj.SimState, 'running')
                obj.pause();
                wasRunning = true;
            else
                wasRunning = false;
            end

            % 简化的时间跳转：重置到目标时间
            obj.SimTime = targetTime;
            obj.SimInfo.t = targetTime;

            % 更新显示
            obj.updateVisualization();
            obj.updateInfoPanels();

            if wasRunning
                obj.resume();
            end

            fprintf('跳转到时间: %.1f 秒\n', targetTime);
        end

        function stateStr = getStateString(obj)
            % 获取状态字符串
            switch obj.SimState
                case 'stopped'
                    stateStr = '已停止';
                case 'running'
                    stateStr = '运行中';
                case 'paused'
                    stateStr = '已暂停';
                case 'finished'
                    stateStr = '已完成';
                otherwise
                    stateStr = '未知';
            end
        end

        function fps = calculateCurrentFPS(obj)
            % 计算当前FPS
            persistent lastTime frameCounter;

            if isempty(lastTime)
                lastTime = tic;
                frameCounter = 0;
                fps = 0;
                return;
            end

            frameCounter = frameCounter + 1;
            elapsed = toc(lastTime);

            if elapsed >= 1.0  % 每秒更新一次FPS
                fps = frameCounter / elapsed;
                lastTime = tic;
                frameCounter = 0;
            else
                fps = frameCounter / elapsed;
            end
        end

        function closeCallback(obj)
            % 窗口关闭回调
            choice = questdlg('确定要关闭实时仿真引擎吗？', ...
                             '确认关闭', ...
                             '是', '否', '否');

            if strcmp(choice, '是')
                obj.cleanup();
                delete(obj.MainFigure);
            end
        end

        function keyPressCallback(obj, evt)
            % 键盘快捷键回调
            switch evt.Key
                case 'space'
                    obj.togglePlayPause();
                case 'r'
                    obj.reset();
                case 'escape'
                    obj.stop();
                case 'equal'  % + 键
                    newScale = min(5, obj.TimeScale * 1.2);
                    obj.updateTimeScale(newScale);
                    set(obj.SpeedSlider, 'Value', newScale);
                case 'hyphen'  % - 键
                    newScale = max(0.1, obj.TimeScale / 1.2);
                    obj.updateTimeScale(newScale);
                    set(obj.SpeedSlider, 'Value', newScale);
            end
        end

        function cleanup(obj)
            % 清理资源
            try
                if ~isempty(obj.SimulationTimer) && isvalid(obj.SimulationTimer)
                    stop(obj.SimulationTimer);
                    delete(obj.SimulationTimer);
                end

                if ~isempty(obj.RenderTimer) && isvalid(obj.RenderTimer)
                    stop(obj.RenderTimer);
                    delete(obj.RenderTimer);
                end

                if ~isempty(obj.MainFigure) && isvalid(obj.MainFigure)
                    close(obj.MainFigure);
                end
            catch
                % 忽略清理过程中的错误
            end
        end
    end
end
