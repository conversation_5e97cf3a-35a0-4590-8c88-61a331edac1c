% RunLAATSimUI - Executes the LAAT simulation with specified parameters.
%
% Syntax:
%   [scenarioName] = RunLAATSimUI(InflowRate, NewSettings, SceStr)
%
% Inputs:
%   InflowRate  - Numeric value specifying the inflow rate for the simulation.
%   NewSettings - Structure containing new settings or parameters for the simulation.
%   SceStr      - String specifying the scenario name or description.
%
% Outputs:
%   scenarioName - String representing the name of the executed simulation scenario.
%
% Description:
%   This function runs the LAAT simulation using the provided inflow rate,
%   settings, and scenario string. It returns the name of the executed scenario.
%
% Note:
% This function is part of the LAAT simulation framework and is not designed to be run independently. This function is called by Flask API to run the simulation and return the results.
%
% Author: <PERSON><PERSON>
% Date Created: 2024-03-05
function [scenarioName] = RunLAATSimUI(InflowRate,NewSettings,SceStr)
clc; close all; dbstop if error;
close all force; close all hidden;
disp(['Starting Simulation']);
UIRun = 1;
SimInfo.RT.TCP_PostRunningTime = [];
SimInfo.RT.TFCRunningTime = [];
SimInfo.RT.SimStartTime = datetime;
disp(['Simuation started: Time=' datestr(SimInfo.RT.SimStartTime,'yyyy-mm-dd HH:MM:SS.FFF')])
SimFilename = ['_Qin' sprintf('%0.0f',InflowRate) SceStr];
SimInfo.SimOutputDirStr = ['.\Outputs\SimOutput_' datestr(now,'yyyymmdd_hhMMss') SimFilename '\'];
%% Settings
disp(['Determining Setting']);
if (~isempty(NewSettings))
    [Settings.Airspace] = SettingAirspace(double(NewSettings.Airspace.dx),double(NewSettings.Airspace.dy),double(NewSettings.Airspace.dz),NewSettings.Airspace.asStr,UIRun);
    [Settings.Aircraft] = SettingAircraft([double(NewSettings.Aircraft.VmaxMin);double(NewSettings.Aircraft.VmaxMax)],[double(NewSettings.Aircraft.RsMin);double(NewSettings.Aircraft.RsMax)]);
    [Settings.Sim] = SettingSimulation(double(NewSettings.Sim.Qin)/60,10);
    disp(['Inflow aircraft/s:' double(NewSettings.Sim.Qin)/60])
else
    [Settings.Airspace] = SettingAirspace(1500,1500,90,'NYC',UIRun);
    [Settings.Aircraft] = SettingAircraft([10,30],[10,30]);
    [Settings.Sim] = SettingSimulation(InflowRate,10);
end
%% Init Objects
SimInfo.Mina = []; SimInfo.Mque = []; SimInfo.Mact = []; SimInfo.Marr = []; SimInfo.MactBQ = [];
SimInfo.M = 1:1:Settings.Sim.M; SimInfo.cc = 0;
dtS = Settings.Sim.dtsim; dtM = Settings.Sim.dtMFD; tf = Settings.Sim.tf;
SimInfo.dtS = dtS; SimInfo.dtM = dtM; SimInfo.tf = tf;
SimInfo.pdt = (zeros((SimInfo.tf/SimInfo.dtS)+1,3*size(SimInfo.M,2))); SimInfo.vdt = (zeros((SimInfo.tf/SimInfo.dtS)+1,3*size(SimInfo.M,2))); SimInfo.statusdt = (zeros((SimInfo.tf/SimInfo.dtS)+1,size(SimInfo.M,2))); SimInfo.ridt = (zeros((SimInfo.tf/SimInfo.dtS)+1,size(SimInfo.M,2)));
TFC = []; TFC.CS = []; TFC.EC = [];
TFC.EC.ECdt = zeros((SimInfo.tf/SimInfo.dtS)+1,size(SimInfo.M,2)); TFC.EC.sumECtdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.sumECqdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.avgECtdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.avgECqdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1); TFC.EC.sumECdt = zeros((SimInfo.tf/SimInfo.dtS)+1,1);
%% Aircraft Creation
disp(['Initalizing Aircraft']);
[SimInfo,ObjAircraft] = InitAircraftObj(SimInfo,Settings);
%% Export Settings
% save([SimInfo.SimOutputDirStr 'Settings' SimFilename],'-v7.3');
disp(['Initalizing Aircraft']);
%% Simulation
% Start Simulation
for t=0:dtS:tf
    disp(['Running Simulation  [t=' sprintf('%0.1f',t) '/' sprintf('%0.0f',tf) ']'])
    SimInfo.t = t;
    %% Departures
    [SimInfo,ObjAircraft] = AircraftDepartures(SimInfo,ObjAircraft);
    %% Controller + Motion
    [SimInfo,ObjAircraft] = AircraftController(SimInfo,ObjAircraft,Settings);
    %     [SimInfo,ObjAircraft] = AircraftMotion(aa,SimInfo,ObjAircraft,Settings);
    %% Arrival
    [SimInfo,ObjAircraft] = AircraftArrivals(SimInfo,ObjAircraft);
    %% Update SimInfo
    [SimInfo] = UpdateSimInfo(SimInfo,ObjAircraft);
    %% Energy Conspution
    if (t~=0)
        [TFC.EC,ObjAircraft] = CalEC_AG(TFC.EC,SimInfo,ObjAircraft);
    end
    %% TFC
    if (t~=0)&&(mod(t,dtM)==0)
        SimInfo.RT.TFCStartTime = datetime;
        [TFC] = CalTFC_N(TFC,SimInfo,ObjAircraft,Settings);
        [TFC] = CalTFC_Ri(TFC,SimInfo,ObjAircraft,Settings);
        SimInfo.RT.TFCEndTime = datetime;
        SimInfo.RT.TFCRunningTime(end+1) = seconds(SimInfo.RT.TFCEndTime-SimInfo.RT.TFCStartTime);
    end
end
disp(['Finishing Simulation'])
clear t dtS dtM dtC tf
SimInfo.RT.SimEndTime = datetime;
SimInfo.RT.SimRunningTime = seconds(SimInfo.RT.SimEndTime-SimInfo.RT.SimStartTime);
SimInfo.RT.SimRunningTimeStr = datestr(SimInfo.RT.SimEndTime-SimInfo.RT.SimStartTime,'HH:MM:SS.FFF');%seconds(datetime(SimInfo.SimEndTime)-datetime(SimInfo.SimStartTime));
disp(['Simuation Ended: Time=' datestr(SimInfo.RT.SimEndTime,'yyyy-mm-dd HH:MM:SS.FFF')])
disp(['TFC RunningTime: Time=' num2str(sum(SimInfo.RT.TFCRunningTime)) ' [seconds]'])
disp(['TCP_Post RunningTime: Time=' num2str(sum(SimInfo.RT.TCP_PostRunningTime)) ' [seconds]'])
disp(['Exporting Data'])
%% Exporting and Plotting
% Export Workspace
scenarioName = ExportJSON(['./public/Outputs/' 'SimOutput_' SceStr],SimInfo,ObjAircraft,TFC,Settings);
disp(scenarioName)
disp(['Finishing Simulation'])
% % Export Video
TTS_Final = TFC.N.cumTTS(end)/3600;
end