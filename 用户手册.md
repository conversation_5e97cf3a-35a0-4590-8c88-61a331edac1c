# pyLAATSimV0200 用户手册

## 简介

pyLAATSimV0200 是一个专业的低空空域交通仿真系统，用于研究城市空中交通（UAM）和垂直起降飞行器（VTOL）的交通流特性。

## 快速上手

### 1. 环境准备
- 安装 MATLAB R2018b 或更高版本
- 确保有足够的内存（建议8GB以上）

### 2. 运行第一个仿真

**标准仿真模式：**
```matlab
% 在 MATLAB 命令窗口中输入：
RunLAATSim(10/60, 'MyFirstTest', 'NYC');
```

**实时可视化模式：**
```matlab
% 启动带实时可视化的仿真：
RunLAATSimRealTime(10/60, 'MyFirstTest', 'NYC');
```

**真正的实时仿真引擎（强烈推荐）：**
```matlab
% 启动革命性的实时仿真引擎：
engine = RunLAATSimRealTimeEngine(10/60, 'MyFirstTest', 'NYC');

% 或者运行演示程序：
DemoRealTimeSimulation();
```

实时仿真引擎提供真正的事件驱动仿真，支持暂停/继续、速度调节、时间跳转等交互功能。

### 3. 查看结果
- **实时模式**：在仿真运行过程中直接观察可视化窗口
- **标准模式**：仿真完成后，检查 `Outputs/` 目录：
  - `.avi` 文件：飞行轨迹动画
  - `.mat` 文件：详细仿真数据
  - `.json` 文件：结构化结构数据

## 主要功能

### 仿真场景
- **NYC**：纽约场景（高空400米）
- **SF**：旧金山场景
- **PAR**：巴黎场景
- **HK**：香港场景
- **LI**：长岛场景（支持固定航路）

### 飞行器类型
- **PAV (3)**：个人飞行器（默认）
- **UAV (4)**：无人机
- **EPAV (1)**：电动个人飞行器
- **EUAV (2)**：电动无人机

## 常用参数设置

### 基本参数
```matlab
% 流入率设置（架/秒）
InflowRate = 15/60;  % 每分钟15架

% 空域尺寸（米）
dx = 2000;  % 宽度
dy = 2000;  % 长度  
dz = 120;   % 高度

% 飞行器参数
VmaxRange = [15, 25];  % 速度范围 (m/s)
RsRange = [8, 12];     % 安全半径范围 (m)
```

### 自定义仿真示例
```matlab
% 创建自定义设置
NewSettings.Airspace.dx = 1500;
NewSettings.Airspace.dy = 1500;
NewSettings.Airspace.dz = 90;
NewSettings.Airspace.asStr = 'SF';

NewSettings.Aircraft.VmaxMin = 20;
NewSettings.Aircraft.VmaxMax = 30;
NewSettings.Aircraft.RsMin = 10;
NewSettings.Aircraft.RsMax = 15;

NewSettings.Sim.Qin = 25;  % 每小时25架

% 运行仿真
scenarioName = RunLAATSimUI(25/60, NewSettings, 'CustomTest');
```

## 结果分析

### 输出文件说明
1. **轨迹数据** (`Trajectories_*.mat`)
   - 包含所有飞行器的完整轨迹信息
   - 可用于后续分析和可视化

2. **动画视频** (`MotionPicture_*.avi`)
   - 显示飞行器在3D空域中的运动
   - 包含交通流统计信息

3. **JSON数据** (`*_*.json`)
   - 结构化的仿真结果
   - 便于与其他系统集成

### 关键指标
- **TTS (Total Time Spent)**：总飞行时间
- **交通流密度**：单位空域内的飞行器数量
- **能耗统计**：电池使用情况
- **避碰事件**：安全性评估

## 性能优化

### 提高仿真速度
1. **减少飞行器数量**
   ```matlab
   InflowRate = 5/60;  % 降低流入率
   ```

2. **调整时间步长**
   ```matlab
   % 在 SettingSimulation.m 中修改
   Sim.dtsim = 1.0;  % 增大时间步长
   ```

3. **简化可视化**
   - 关闭实时绘图
   - 降低视频帧率

### 内存管理
- 监控 MATLAB 内存使用
- 及时清理不需要的变量
- 考虑分批处理大规模仿真

## 故障排除

### 常见错误
1. **内存不足**
   - 减少飞行器数量
   - 缩小空域尺寸
   - 增加系统内存

2. **函数找不到**
   - 检查 MATLAB 路径设置
   - 确保所有文件完整

3. **仿真结果异常**
   - 检查参数设置是否合理
   - 验证输入数据格式
   - 查看 MATLAB 命令窗口的错误信息

### 调试技巧
```matlab
% 启用调试模式
dbstop if error

% 检查关键变量
disp(['飞行器数量: ' num2str(length(ObjAircraft))])
disp(['仿真时长: ' num2str(Settings.Sim.tf) ' 秒'])
```

## 高级功能

### 自定义避碰算法
修改 `AircraftController.m` 中的参数：
```matlab
gamma = 1.5;    % 势场强度
k2 = 1.2;       % 控制增益
```

### 添加新场景
1. 在 `LoadVertiports.m` 中添加新的城市配置
2. 创建对应的 JSON 配置文件
3. 更新 `SettingAirspace.m` 中的逻辑

### 能耗模型调整
在 `SettingAircraft.m` 中修改：
```matlab
Aircraft.Bat_max = 100;     % 电池容量 (Wh)
Aircraft.Bat_limit = 20;    % 安全余量 (Wh)
```

## 最佳实践

### 仿真设计
1. **从小规模开始**：先用少量飞行器测试
2. **逐步增加复杂度**：确认基本功能正常后再扩展
3. **保存中间结果**：定期保存重要的仿真配置

### 数据管理
1. **规范命名**：使用有意义的场景名称
2. **定期清理**：删除不需要的输出文件
3. **备份重要结果**：保存关键的仿真数据

### 性能监控
```matlab
% 监控仿真性能
tic;  % 开始计时
RunLAATSim(10/60, 'PerfTest', 'NYC');
elapsed_time = toc;  % 结束计时
disp(['仿真用时: ' num2str(elapsed_time) ' 秒']);
```

## 扩展开发

### 添加新功能
1. 遵循现有的代码结构
2. 添加详细的函数注释
3. 进行充分的测试

### 集成外部系统
- 使用 JSON 接口进行数据交换
- 通过 `RunLAATSimUI` 实现 Web 集成
- 考虑使用 MATLAB Compiler 生成独立应用

## 技术支持

### 获取帮助
1. 查阅本用户手册
2. 检查 `README_CN.md` 详细文档
3. 在 GitHub 上提交 Issue

### 社区资源
- 学术论文和研究报告
- 相关的开源项目
- 专业论坛和讨论组

---

**提示**：建议在开始大规模仿真前，先用小参数进行测试，确保系统配置正确。

*版本：v0.2.0 | 更新日期：2024年3月*
